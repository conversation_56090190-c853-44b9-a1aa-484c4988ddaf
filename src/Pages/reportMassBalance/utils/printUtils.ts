import { TableData } from "../models/table.type";


export const generatePrintHTML = (data: TableData, reportTitle: string = "Balance de Masas", period?: string): string => {

  const now = new Date();
  const formattedDate = now.toLocaleDateString('es-ES', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
  const formattedTime = now.toLocaleTimeString('es-ES', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });
  const generatedTimestamp = `Generado el ${formattedDate} ${formattedTime}`;

  const printStyles = `
    <style>
      @page { 
        size: A4 landscape; 
        margin: 0.4in 0.5in 0.6in 0.5in;
      }
      
      @page {
        @top-left {
          content: "Página " counter(page) " / 2";
          font-size: 12px;
          color: #666;
        }
        @top-center {
          content: "Balance de Masas - ${period || 'Reporte'}";
          font-size: 18px;
          font-weight: bold;
          color: #333;
          margin-bottom: 10px;
        }
        @top-right {
          content: "${generatedTimestamp}";
          font-size: 12px;
          color: #666;
        }
      }
      
      .print-no-break {
        page-break-inside: avoid;
      }
      
      .print-force-page-break {
        page-break-before: always;
      }
    </style>
  `;
  
  const massBalanceTableHTML = `
    <div class="mb-4 print-no-break">
      <table class="w-full mb-5 bg-white border border-gray-300 rounded-xl overflow-hidden" style="border-collapse: separate; border-spacing: 0;">
        <thead>
          <tr>
            <th rowspan="2" class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">Área de Prestación</th>
            <th rowspan="2" class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">Nombre de Área de Prestación</th>
            <th colspan="6" class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">F14 Toneladas Provenientes del Área de Prestación</th>
            <th colspan="3" class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">F34 Disposición Final Operador</th>
            <th rowspan="2" class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">Diferencia (F34-F14)</th>
          </tr>
          <tr>
            <th class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">Toneladas de Limpieza Urbana</th>
            <th class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">Toneladas de Barrido</th>
            <th class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">Toneladas de Residuos No Aprovechables</th>
            <th class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">Toneladas de Rechazos de Residuos Aprovechados</th>
            <th class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">Toneladas de Residuos Aprovechables</th>
            <th class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">Toneladas de Barrido + Toneladas de Residuos No Aprovechables</th>
            <th class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">Total por NUAP</th>
            <th class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">Descuentos</th>
            <th class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">Total por NUAP - Descuentos</th>
          </tr>
        </thead>
        <tbody>
          ${data.massBalance.map(item => `
            <tr>
              <td class="p-2 text-center border-b border-r border-gray-300 text-xs">${item.serviceArea}</td>
              <td class="p-2 text-center border-b border-r border-gray-300 text-xs">${item.nuap}</td>
              <td class="p-2 text-center border-b border-r border-gray-300 text-xs">${item.urbanCleaningTons}</td>
              <td class="p-2 text-center border-b border-r border-gray-300 text-xs">${item.sweepingTons}</td>
              <td class="p-2 text-center border-b border-r border-gray-300 text-xs">${item.nonRecyclableTons}</td>
              <td class="p-2 text-center border-b border-r border-gray-300 text-xs">${item.rejectionTons}</td>
              <td class="p-2 text-center border-b border-r border-gray-300 text-xs">${item.recyclableTons}</td>
              <td class="p-2 text-center border-b border-r border-gray-300 text-xs">${item.totalTons}</td>
              <td class="p-2 text-center border-b border-r border-gray-300 text-xs">${item.totalByNuap}</td>
              <td class="p-2 text-center border-b border-r border-gray-300 text-xs">${item.discountTons}</td>
              <td class="p-2 text-center border-b border-r border-gray-300 text-xs">${item.totalByNuapDiscounts}</td>
              <td class="p-2 text-center border-b border-r border-gray-300 text-xs">${item.difference}</td>
            </tr>
          `).join('')}
        </tbody>
        <tfoot>
          <tr>
            <td class="p-2 text-center border-b border-r border-gray-300 text-xs bg-gray-100 font-bold text-black">SUMA</td>
            <td class="p-2 text-center border-b border-r border-gray-300 text-xs bg-gray-100 font-bold text-black">TOTAL</td>
            <td class="p-2 text-center border-b border-r border-gray-300 text-xs bg-gray-100 font-bold text-black">${data.massBalanceTotals.allUrbanCleaningTons}</td>
            <td class="p-2 text-center border-b border-r border-gray-300 text-xs bg-gray-100 font-bold text-black">${data.massBalanceTotals.allSweepingTons}</td>
            <td class="p-2 text-center border-b border-r border-gray-300 text-xs bg-gray-100 font-bold text-black">${data.massBalanceTotals.allNonRecyclableTons}</td>
            <td class="p-2 text-center border-b border-r border-gray-300 text-xs bg-gray-100 font-bold text-black">${data.massBalanceTotals.allRejectionTons}</td>
            <td class="p-2 text-center border-b border-r border-gray-300 text-xs bg-gray-100 font-bold text-black">${data.massBalanceTotals.allRecyclableTons}</td>
            <td class="p-2 text-center border-b border-r border-gray-300 text-xs bg-gray-100 font-bold text-black">${data.massBalanceTotals.totalTonsF14}</td>
            <td class="p-2 text-center border-b border-r border-gray-300 text-xs bg-gray-100 font-bold text-black">${data.massBalanceTotals.totalTonsF34}</td>
            <td class="p-2 text-center border-b border-r border-gray-300 text-xs bg-gray-100 font-bold text-black">${data.massBalanceTotals.allDiscountTons}</td>
            <td class="p-2 text-center border-b border-r border-gray-300 text-xs bg-gray-100 font-bold text-black">${data.massBalanceTotals.allTotalsMinusDiscounts}</td>
            <td class="p-2 text-center border-b border-r border-gray-300 text-xs bg-gray-100 font-bold text-black">${data.massBalanceTotals.totalDifference}</td>
          </tr>
        </tfoot>
      </table>
    </div>
  `;

  const finalDispositionTableHTML = `
    <div class="mb-4 print-no-break">
      <table class="w-full mb-5 bg-white border border-gray-300 rounded-xl overflow-hidden" style="border-collapse: separate; border-spacing: 0;">
        <thead>
          <tr>
            <th colspan="4" class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">Disposición Final</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <th class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">Total Disposición Final</th>
            <td class="p-2 text-center border-b border-r border-gray-300 text-xs">${data.finalDispositionTotals.total}</td>
            <th class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">Total Emvarias</th>
            <td class="p-2 text-center border-b border-r border-gray-300 text-xs">${data.finalDispositionTotals.emvariasTotal}</td>
          </tr>
        </tbody>
      </table>
    </div>
  `;

  const distributionTableHTML = `
    <div class="mb-4 mt-8 print-no-break print-force-page-break">
      <table class="w-full mb-5 bg-white border border-gray-300 rounded-xl overflow-hidden" style="border-collapse: separate; border-spacing: 0;">
        <thead>
          <tr>
            <th colspan="7" class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">Distribución</th>
          </tr>
          <tr>
            <th class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">Área de Prestación</th>
            <th class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">Toneladas Facturadas</th>
            <th class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">Nro Viajes</th>
            <th class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">Tonxviaje</th>
            <th class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">Toneladas Totales Rutas Compartidas</th>
            <th class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">% Distpeaje</th>
            <th class="p-2 text-center border-b border-r border-gray-300 text-xs bg-white text-black font-bold">Toneladas a Compensar</th>
          </tr>
        </thead>
        <tbody>
          ${data.distributions.map(item => `
            <tr>
              <td class="p-2 text-center border-b border-r border-gray-300 text-xs">${item.serviceArea}</td>
              <td class="p-2 text-center border-b border-r border-gray-300 text-xs">${item.tons}</td>
              <td class="p-2 text-center border-b border-r border-gray-300 text-xs">${item.trips}</td>
              <td class="p-2 text-center border-b border-r border-gray-300 text-xs">${item.tonPerTrip}</td>
              <td class="p-2 text-center border-b border-r border-gray-300 text-xs">${item.tollSharedRouteTons}</td>
              <td class="p-2 text-center border-b border-r border-gray-300 text-xs">${item.tollPercentage}</td>
              <td class="p-2 text-center border-b border-r border-gray-300 text-xs">${item.compensationTons}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </div>
  `;

  return `
    <!DOCTYPE html>
    <html lang="es">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${reportTitle}</title>
      <script src="https://cdn.tailwindcss.com"></script>
      ${printStyles}
    </head>
    <body class="font-sans text-xs leading-relaxed text-black bg-white m-0 p-0" style="font-size: 12px; line-height: 1.4;">
    <div class="print:hidden bg-gray-500 block w-full p-2">
      <h1 class="print:hidden text-white font-bold text-center m-0">Previsualización de PDF:</h1>
    </div>
    
      <div class="mt-10 px-3">
        ${massBalanceTableHTML}
        ${finalDispositionTableHTML}
        ${distributionTableHTML}
      </div>
    </body>
    </html>
  `;
};


export const openPrintWindow = (data: TableData, reportTitle?: string, period?: string): void => {

  const now = new Date();
  const formattedDateTime = now.toLocaleString(undefined, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });

  const dynamicTitle = `Balance de Masas - ${formattedDateTime}`;
  const printHTML = generatePrintHTML(data, reportTitle || dynamicTitle, period);

  const detectedMonitorWidth = window.outerWidth;
  const detectedMonitorHeight = window.outerHeight;

  const printWindow = window.open('', '_blank', `width=${detectedMonitorWidth},height=${detectedMonitorHeight},scrollbars=yes,resizable=yes`);

  if (!printWindow) {
    alert('Por favor, permite las ventanas emergentes para imprimir el reporte.');
    return;
  }

  printWindow.document.writeln(printHTML);
  printWindow.document.close();
  printWindow.document.title = dynamicTitle;
  
  printWindow.onload = () => {
    setTimeout(() => {
      printWindow.focus();
      printWindow.print();
      
      setTimeout(() => {
        if (!printWindow.closed) {
          printWindow.close();
        }
      }, 100);
    }, 100);
  };
};
