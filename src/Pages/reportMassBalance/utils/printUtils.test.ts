import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { generatePrintHTML, openPrintWindow } from './printUtils';
import { TableData } from '../models/table.type';

const mockTableData: TableData = {
  massBalance: [
    {
      nuap: "Test NUAP",
      serviceArea: "Test Area",
      urbanCleaningTons: "100.00",
      sweepingTons: "50.00",
      nonRecyclableTons: "75.00",
      rejectionTons: "25.00",
      recyclableTons: "30.00",
      totalTons: "280.00",
      totalByNuap: "300.00",
      discountTons: "20.00",
      totalByNuapDiscounts: "280.00",
      difference: "0.00"
    }
  ],
  massBalanceTotals: {
    allUrbanCleaningTons: "100.00",
    allSweepingTons: "50.00",
    allNonRecyclableTons: "75.00",
    allRejectionTons: "25.00",
    allRecyclableTons: "30.00",
    totalTonsF14: "280.00",
    allDiscountTons: "20.00",
    allTotalsMinusDiscounts: "280.00",
    totalTonsF34: "300.00",
    totalDifference: "0.00"
  },
  finalDispositionTotals: {
    total: "300.00",
    emvariasTotal: "280.00"
  },
  distributions: [
    {
      serviceArea: "Test Distribution Area",
      tons: "100.00",
      trips: "10",
      tonPerTrip: "10.00",
      tollSharedRouteTons: "50.00",
      tollPercentage: "50",
      compensationTons: "5.00"
    }
  ],
  isValid: true
};

describe('printUtils', () => {
  describe('generatePrintHTML', () => {
    it('should generate valid HTML with default title', () => {
      const html = generatePrintHTML(mockTableData);
      
      expect(html).toContain('<!DOCTYPE html>');
      expect(html).toContain('<html lang="es">');
      expect(html).toContain('<title>Balance de Masas</title>');
      expect(html).toContain('Test NUAP');
      expect(html).toContain('Test Area');
      expect(html).toContain('100.00');
    });

    it('should generate HTML with custom title', () => {
      const customTitle = 'Custom Report Title';
      const html = generatePrintHTML(mockTableData, customTitle);
      
      expect(html).toContain(`<title>${customTitle}</title>`);
  
      expect(html).not.toContain(`<h1 class="text-center text-lg font-bold mb-5 text-black">${customTitle}</h1>`);
    });

    it('should include period header when period is provided', () => {
      const period = '2024/11';
      const html = generatePrintHTML(mockTableData, 'Test Title', period);
      
      expect(html).toContain(`Balance de Masas - ${period}`);
      expect(html).toContain('<h2 style="font-size: 24px; font-weight: bold; color: #333; margin: 0;">');
    });

    it('should not include period header when period is not provided', () => {
      const html = generatePrintHTML(mockTableData, 'Test Title');
      
      expect(html).not.toContain('Balance de Masas -');
    });

    it('should include all mass balance data', () => {
      const html = generatePrintHTML(mockTableData);
      
  
      expect(html).toContain('Test NUAP');
      expect(html).toContain('Test Area');
      expect(html).toContain('100.00');
      expect(html).toContain('50.00');
      expect(html).toContain('75.00');
    });

    it('should include final disposition data', () => {
      const html = generatePrintHTML(mockTableData);
      
      expect(html).toContain('Disposición Final');
      expect(html).toContain('300.00');
      expect(html).toContain('280.00');
    });

    it('should include distribution data', () => {
      const html = generatePrintHTML(mockTableData);
      
      expect(html).toContain('Distribución');
      expect(html).toContain('Test Distribution Area');
      expect(html).toContain('10');
    });

    it('should include print styles and Tailwind classes', () => {
      const html = generatePrintHTML(mockTableData);
      
      expect(html).toContain('@media print');
      expect(html).toContain('tailwindcss.com');
      expect(html).toContain('bg-white');
      expect(html).toContain('text-black');
      expect(html).toContain('border-gray-300');
      expect(html).toContain('rounded-xl');
    });
  });

  describe('openPrintWindow', () => {
    let mockWindow: any;
    let originalWindow: any;

    beforeEach(() => {
  
      mockWindow = {
        document: {
          write: vi.fn(),
          close: vi.fn()
        },
        focus: vi.fn(),
        print: vi.fn(),
        closed: false,
        onload: null
      };

      originalWindow = global.window;
      global.window = {
        ...originalWindow,
        open: vi.fn().mockReturnValue(mockWindow),
        outerWidth: 1200,
        outerHeight: 800
      } as any;

  
      global.alert = vi.fn();
    });

    afterEach(() => {
      global.window = originalWindow;
      vi.clearAllMocks();
    });

    it('should open a new window with correct parameters', () => {
      openPrintWindow(mockTableData);
      
      expect(window.open).toHaveBeenCalledWith(
        '',
        '_blank',
        'width=1200,height=800,scrollbars=yes,resizable=yes'
      );
    });

    it('should write HTML content to the new window', () => {
      openPrintWindow(mockTableData);
      
      expect(mockWindow.document.write).toHaveBeenCalledWith(
        expect.stringContaining('<!DOCTYPE html>')
      );
      expect(mockWindow.document.close).toHaveBeenCalled();
    });

    it('should handle popup blocker scenario', () => {
      (window.open as any).mockReturnValue(null);
      
      openPrintWindow(mockTableData);
      
      expect(global.alert).toHaveBeenCalledWith(
        'Por favor, permite las ventanas emergentes para imprimir el reporte.'
      );
    });

    it('should use custom title when provided', () => {
      const customTitle = 'Custom Title';
      openPrintWindow(mockTableData, customTitle);
      
      expect(mockWindow.document.write).toHaveBeenCalledWith(
        expect.stringContaining(customTitle)
      );
    });

    it('should include period in HTML when provided', () => {
      const period = '2024/11';
      openPrintWindow(mockTableData, 'Test Title', period);
      
      expect(mockWindow.document.write).toHaveBeenCalledWith(
        expect.stringContaining(`Balance de Masas - ${period}`)
      );
    });

    it('should not include period header when period is not provided', () => {
      openPrintWindow(mockTableData, 'Test Title');
      
      expect(mockWindow.document.write).toHaveBeenCalledWith(
        expect.not.stringContaining('Balance de Masas -')
      );
    });

    it('should trigger print after window loads', () => {
      vi.useFakeTimers();
      
      openPrintWindow(mockTableData);
      

      if (mockWindow.onload) {
        mockWindow.onload();
      }
      

      vi.advanceTimersByTime(500);
      
      expect(mockWindow.focus).toHaveBeenCalled();
      expect(mockWindow.print).toHaveBeenCalled();
      
      vi.useRealTimers();
    });
  });
});
