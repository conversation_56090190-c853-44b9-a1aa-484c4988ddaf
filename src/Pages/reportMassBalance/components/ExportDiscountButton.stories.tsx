import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import ExportDiscountButton from './ExportDiscountButton';


const meta: Meta<typeof ExportDiscountButton> = {
  title: 'MassBalance/Molecule/ExportDiscountButton',
  component: ExportDiscountButton,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
          **ExportDiscountButton Component**
          
          A specialized button for exporting discount reports from the Mass Balance module.
          
          **Features:**
          - Downloads discount data in Excel (.xlsx) format
          - Loading state with animated download icon
          - Automatic timeout for loading state reset
          - Integration with backend download endpoints
          - Material-UI styling with consistent design
          - User feedback during download process
        `
      }
    }
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div className="p-4">
        <Story />
      </div>
    )
  ]
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {};

export const Interactive: Story = {
  parameters: {
    docs: {
      description: {
        story: `
          Click the button to see the loading animation.
          In the real application, this would trigger a file download.
        `
      }
    }
  }
};

export const States: Story = {
  render: () => (
    <div className="flex gap-4 flex-col">
      <div>
        <h3 className="mb-2 font-semibold">Normal State</h3>
        <ExportDiscountButton />
      </div>
      <div>
        <h3 className="mb-2 font-semibold">In Context</h3>
        <div className="bg-gray-50 p-4 rounded border">
          <p className="mb-4 text-sm text-gray-600">
            This button typically appears in the Mass Balance report header alongside other export options.
          </p>
          <ExportDiscountButton />
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: `
          Shows the export button in different contexts and states.
        `
      }
    }
  }
};