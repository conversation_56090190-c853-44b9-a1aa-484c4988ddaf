import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import MassBalanceDrawerActions from './MassBalanceDrawerActions';


const meta: Meta<typeof MassBalanceDrawerActions> = {
  title: 'MassBalance/Molecule/MassBalanceDrawerActions',
  component: MassBalanceDrawerActions,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
          **MassBalanceDrawerActions Component**
          
          A set of action buttons specifically designed for the Mass Balance filter drawer.
          
          **Features:**
          - "Generar balance" button to apply filters and generate the report
          - "Limpiar todos" button to clear all applied filters
          - Disabled state support for both buttons
          - Consistent styling with purple theme
          - Responsive design with proper spacing
          - Hover and focus states for better UX
          - Accessibility features with proper focus management
        `
      }
    }
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div className="p-6 bg-gray-50 min-h-[200px] flex items-center justify-center">
        <Story />
      </div>
    )
  ],
  argTypes: {
    handleApplyFilters: {
      description: 'Function called when the "Generar balance" button is clicked',
      action: 'apply-filters'
    },
    handleClearFilters: {
      description: 'Function called when the "Limpiar todos" button is clicked',
      action: 'clear-filters'
    },
    isDisabled: {
      description: 'Whether both buttons should be disabled',
      control: { type: 'boolean' }
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    handleApplyFilters: fn(),
    handleClearFilters: fn(),
    isDisabled: false
  }
};

export const Disabled: Story = {
  args: {
    handleApplyFilters: fn(),
    handleClearFilters: fn(),
    isDisabled: true
  }
};

export const InDrawerContext: Story = {
  args: {
    handleApplyFilters: fn(),
    handleClearFilters: fn(),
    isDisabled: false
  },
  decorators: [
    (Story) => (
      <div className="w-80 bg-white border-l shadow-lg">
        <div className="p-6">
          <h3 className="text-lg font-semibold mb-4">Filtros</h3>
          <div className="space-y-4 mb-6">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Período</label>
              <input 
                type="month" 
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                defaultValue="2024-11"
              />
            </div>
          </div>
          <Story />
        </div>
      </div>
    )
  ],
  parameters: {
    docs: {
      description: {
        story: `
          Shows the action buttons in their typical context within a filter drawer.
        `
      }
    }
  }
};

export const Interactive: Story = {
  args: {
    handleApplyFilters: fn(),
    handleClearFilters: fn(),
    isDisabled: false
  },
  parameters: {
    docs: {
      description: {
        story: `
          Click the buttons to see the interaction feedback.
          Check the Actions panel to see the function calls.
        `
      }
    }
  }
};