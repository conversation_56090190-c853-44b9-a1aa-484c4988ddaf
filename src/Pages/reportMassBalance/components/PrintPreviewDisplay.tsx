import React from 'react';
import { TableData } from '../models/table.type';
import { generatePrintHTML } from '../utils/printUtils';


interface PrintPreviewDisplayProps {
  data: TableData;
  reportTitle?: string;
  period?: string;
}

const PrintPreviewDisplay: React.FC<PrintPreviewDisplayProps> = ({ 
  data, 
  reportTitle = "Balance de Masas",
  period
}) => {
  const htmlContent = generatePrintHTML(data, reportTitle, period);

  return (
    <div className="w-full h-screen border border-gray-300 rounded-lg overflow-hidden">
      <iframe
        srcDoc={htmlContent}
        className="w-full h-full"
        title="Print Preview"
        style={{
          transform: 'scale(0.8)',
          transformOrigin: 'top left',
          width: '125%',
          height: '125%'
        }}
      />
    </div>
  );
};

export default PrintPreviewDisplay;