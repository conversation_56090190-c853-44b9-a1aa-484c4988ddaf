import type { Meta, StoryObj } from '@storybook/react';
import MassBalanceTableItem from './MassBalanceTableItem';
import { BalanceMassTableRow } from '../models';


const mockTableRow: BalanceMassTableRow = {
  nuap: "NUAP Test 001",
  serviceArea: "Área de Prestación Norte",
  urbanCleaningTons: "150.25",
  sweepingTons: "75.50",
  nonRecyclableTons: "120.75",
  rejectionTons: "30.25",
  recyclableTons: "45.00",
  totalTons: "421.75",
  totalByNuap: "450.00",
  discountTons: "25.50",
  totalByNuapDiscounts: "424.50",
  difference: "2.75"
};

const mockNegativeDifferenceRow: BalanceMassTableRow = {
  nuap: "NUAP Test 002",
  serviceArea: "Área de Prestación Sur",
  urbanCleaningTons: "200.00",
  sweepingTons: "100.25",
  nonRecyclableTons: "180.50",
  rejectionTons: "40.75",
  recyclableTons: "60.00",
  totalTons: "581.50",
  totalByNuap: "600.00",
  discountTons: "35.25",
  totalByNuapDiscounts: "564.75",
  difference: "-16.75"
};

const meta: Meta<typeof MassBalanceTableItem> = {
  title: 'MassBalance/Atom/MassBalanceTableItem',
  component: MassBalanceTableItem,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
          **MassBalanceTableItem Component**
          
          A table row component that displays mass balance data for a specific service area.
          
          **Features:**
          - Displays all mass balance calculations (F14, F34, differences)
          - Responsive table styling with Tailwind CSS
          - Alternating row colors for better readability
          - Sticky right column for balance differences
          - Proper border styling and spacing
        `
      }
    }
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <table className="border border-gray-300 border-separate border-spacing-0">
        <thead>
          <tr className="bg-gray-100">
            <th className="p-2 border-b">Área de Prestación</th>
            <th className="p-2 border-b">NUAP</th>
            <th className="p-2 border-b">Limpieza Urbana</th>
            <th className="p-2 border-b">Barrido</th>
            <th className="p-2 border-b">No Reciclable</th>
            <th className="p-2 border-b">Rechazo</th>
            <th className="p-2 border-b">Reciclable</th>
            <th className="p-2 border-b">Total F14</th>
            <th className="p-2 border-b">Total F34</th>
            <th className="p-2 border-b">Descuentos</th>
            <th className="p-2 border-b">Total - Descuentos</th>
            <th className="p-2 border-b">Diferencia</th>
          </tr>
        </thead>
        <tbody>
          <Story />
        </tbody>
      </table>
    )
  ],
  argTypes: {
    item: {
      description: 'Mass balance data for a single row',
      control: { type: 'object' }
    },
    index: {
      description: 'Row index for styling purposes',
      control: { type: 'number' }
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    item: mockTableRow,
    index: 0
  }
};

export const NegativeDifference: Story = {
  args: {
    item: mockNegativeDifferenceRow,
    index: 1
  }
};

export const EvenRow: Story = {
  args: {
    item: mockTableRow,
    index: 1
  }
};