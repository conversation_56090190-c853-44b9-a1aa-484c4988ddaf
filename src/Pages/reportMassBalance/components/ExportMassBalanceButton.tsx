import { useEffect, useState } from "react";
import { Button, Typography } from "@mui/material";
import GridOnIcon from "@mui/icons-material/GridOn";

import { downloadTable } from "@/helpers/downloadTable";
import { useUrlReportMassBalanceStore } from "@/Pages/reportMassBalance/store/useUrlReportMassBalanceStore";
import { VITE_BASE_URL_MERCURY, endpointsPathsBussiness } from "@/constants";

const ExportMassBalanceButton = () => {
  const [downloadUri, setDownloadUri] = useState("");
  const balanceMassUri = useUrlReportMassBalanceStore((state) => state.downloadReportUrl);

  useEffect(() => {
    const dateFilters = balanceMassUri.indexOf("?") > 0 ? balanceMassUri.split("?")[1] : "";
    setDownloadUri(
      `${VITE_BASE_URL_MERCURY}${endpointsPathsBussiness.MASS_BALANCE_EXPORT}?${dateFilters}`,
    );
  }, [balanceMassUri]);

  const [isDownloading, setIsDownloading] = useState(false);

  const handleClick = () => {
    setIsDownloading(true);

    downloadTable(downloadUri, "xlsx");

    setTimeout(() => {
      setIsDownloading(false);
    }, 3000);
  };

  return (
    <div>
      <Button
        disabled={isDownloading}
        id="export-mass-balance-button"
        startIcon={<GridOnIcon className={!isDownloading ? "" : "animate-ping text-[#28a745]"} />}
        onClick={handleClick}
      >
        <Typography
          className="text-lg font-bold"
          color={isDownloading ? "textSecondary" : "primary"}
        >
          {isDownloading ? "Exportando..." : "Exportar a Excel"}
        </Typography>
      </Button>
    </div>
  );
};

export default ExportMassBalanceButton;
