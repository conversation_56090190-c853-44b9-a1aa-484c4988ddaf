import type { Meta, StoryObj } from "@storybook/react";
import ExportMassBalanceButton from "./ExportMassBalanceButton";

const meta: Meta<typeof ExportMassBalanceButton> = {
  title: "Pages/ReportMassBalance/ExportMassBalanceButton",
  component: ExportMassBalanceButton,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {};

export const Downloading: Story = {
  parameters: {
    docs: {
      description: {
        story: "Button state while downloading the mass balance Excel file. The component sends fromDate and toDate as query parameters to the API endpoint.",
      },
    },
  },
};
