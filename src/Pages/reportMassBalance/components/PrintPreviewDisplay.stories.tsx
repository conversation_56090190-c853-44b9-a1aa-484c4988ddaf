import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import PrintPreviewDisplay from './PrintPreviewDisplay';
import { TableData } from '../models';


const mockTableData: TableData = {
  massBalance: [
    {
      nuap: "NUAP001",
      serviceArea: "Área Norte",
      urbanCleaningTons: "150.25",
      sweepingTons: "75.50",
      nonRecyclableTons: "120.75",
      rejectionTons: "30.25",
      recyclableTons: "45.00",
      totalTons: "421.75",
      totalByNuap: "450.00",
      discountTons: "25.50",
      totalByNuapDiscounts: "424.50",
      difference: "2.75"
    },
    {
      nuap: "NUAP002",
      serviceArea: "Área Sur",
      urbanCleaningTons: "200.00",
      sweepingTons: "100.25",
      nonRecyclableTons: "180.50",
      rejectionTons: "40.75",
      recyclableTons: "60.00",
      totalTons: "581.50",
      totalByNuap: "600.00",
      discountTons: "35.25",
      totalByNuapDiscounts: "564.75",
      difference: "-16.75"
    }
  ],
  massBalanceTotals: {
    allUrbanCleaningTons: "350.25",
    allSweepingTons: "175.75",
    allNonRecyclableTons: "301.25",
    allRejectionTons: "71.00",
    allRecyclableTons: "105.00",
    totalTonsF14: "1003.25",
    totalTonsF34: "1050.00",
    allDiscountTons: "60.75",
    allTotalsMinusDiscounts: "989.25",
    totalDifference: "-14.00"
  },
  distributions: [
    {
      serviceArea: "Área Norte",
      tons: "450.00",
      trips: "45",
      tonPerTrip: "10.00",
      tollSharedRouteTons: "225.00",
      tollPercentage: "50.0",
      compensationTons: "22.50"
    },
    {
      serviceArea: "Área Sur",
      tons: "600.00",
      trips: "60",
      tonPerTrip: "10.00",
      tollSharedRouteTons: "300.00",
      tollPercentage: "50.0",
      compensationTons: "30.00"
    }
  ],
  finalDispositionTotals: {
    total: "1050.00",
    emvariasTotal: "1050.00"
  },
  isValid: true
};


const meta: Meta<typeof PrintPreviewDisplay> = {
  title: 'MassBalance/Template/PrintPreviewDisplay',
  component: PrintPreviewDisplay,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: `
          **PrintPreviewDisplay Template Component**
          
          A template component that displays the complete Mass Balance report in print format.
          
          **Features:**
          - Full print layout preview using iframe
          - Scaled display for better viewing in browser
          - Complete report structure with all tables
          - Print-optimized styling and formatting
          - Responsive design that adapts to container
          - Period header support
          - Professional report formatting
          
          **Use Cases:**
          - Print preview functionality
          - Storybook documentation
          - Testing print layouts
          - Report validation before printing
          
          **Technical Details:**
          - Uses iframe with srcDoc for isolated rendering
          - Applies CSS transforms for optimal viewing
          - Generates complete HTML with embedded styles
          - Supports custom report titles and periods
        `
      }
    }
  },
  tags: ['autodocs'],
  argTypes: {
    data: {
      description: 'Complete mass balance data to display',
      control: { type: 'object' }
    },
    reportTitle: {
      description: 'Title for the report header',
      control: { type: 'text' }
    },
    period: {
      description: 'Period to display in the header (YYYY/MM format)',
      control: { type: 'text' }
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;


export const Default: Story = {
  args: {
    data: mockTableData,
    reportTitle: "Balance de Masas",
    period: "2024/11"
  }
};


export const WithoutPeriod: Story = {
  args: {
    data: mockTableData,
    reportTitle: "Balance de Masas"
  },
  parameters: {
    docs: {
      description: {
        story: `
          Shows the print preview without a period header.
        `
      }
    }
  }
};


export const CustomTitle: Story = {
  args: {
    data: mockTableData,
    reportTitle: "Reporte Personalizado de Balance de Masas",
    period: "2024/12"
  },
  parameters: {
    docs: {
      description: {
        story: `
          Shows the print preview with a custom report title.
        `
      }
    }
  }
};


export const MinimalData: Story = {
  args: {
    data: {
      ...mockTableData,
      massBalance: [mockTableData.massBalance[0]],
      distributions: [mockTableData.distributions[0]]
    },
    reportTitle: "Balance de Masas",
    period: "2024/11"
  },
  parameters: {
    docs: {
      description: {
        story: `
          Shows the print preview with minimal data (single row in each table).
        `
      }
    }
  }
};


export const CompactView: Story = {
  args: {
    data: mockTableData,
    reportTitle: "Balance de Masas",
    period: "2024/11"
  },
  decorators: [
    (Story) => (
      <div className="max-w-4xl mx-auto p-4">
        <Story />
      </div>
    )
  ],
  parameters: {
    docs: {
      description: {
        story: `
          Shows the print preview in a more compact container.
        `
      }
    }
  }
};