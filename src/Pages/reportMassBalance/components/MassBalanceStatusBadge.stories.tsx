import type { Meta, StoryObj } from '@storybook/react';
import MassBalanceStatusBadge from './MassBalanceStatusBadge';


const meta: Meta<typeof MassBalanceStatusBadge> = {
  title: 'MassBalance/Atom/MassBalanceStatusBadge',
  component: MassBalanceStatusBadge,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
          **MassBalanceStatusBadge Component**
          
          A simple status indicator that shows whether the Mass Balance calculations are correct or not.
          
          **Features:**
          - Conditional rendering based on validation state
          - Green styling for correct state with check icon
          - Red styling for incorrect state with cross icon
          - Responsive design with Tailwind CSS
          - Accessible color contrast
        `
      }
    }
  },
  tags: ['autodocs'],
  argTypes: {
    isCorrect: {
      description: 'Whether the mass balance is correct or not',
      control: { type: 'boolean' }
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Correct: Story = {
  args: {
    isCorrect: true
  }
};

export const Incorrect: Story = {
  args: {
    isCorrect: false
  }
};

export const Hidden: Story = {
  args: {
    isCorrect: undefined
  }
};