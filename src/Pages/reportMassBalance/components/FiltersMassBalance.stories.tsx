import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import FiltersMassBalance from './FiltersMassBalance';
import dayjs from 'dayjs';


const defaultProps = {
  anchor: 'right' as const,
  title: 'Filtros',
  isMounted: true,
  clearPeriodFilter: fn(),
  setCurrentSelectedDate: fn()
};


const meta: Meta<typeof FiltersMassBalance> = {
  title: 'MassBalance/Organism/FiltersMassBalance',
  component: FiltersMassBalance,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: `
          **FiltersMassBalance Component**
          
          A comprehensive filter organism that manages date range selection for Mass Balance reports.
          
          **Features:**
          - Date range picker with start and end date selection
          - Integration with Redux global filter state
          - Responsive design with proper spacing
          - Form validation and user feedback
          - Material-UI date picker components
          - Automatic state synchronization
          
          **Architecture:**
          - Uses Redux for state management
          - Connects to global filter slice
          - Handles date validation and formatting
          - Provides real-time filter updates
        `
      }
    }
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div className="max-w-4xl mx-auto p-6 bg-white">
        <Story />
      </div>
    )
  ]
};

export default meta;
type Story = StoryObj<typeof meta>;


export const Default: Story = {
  args: defaultProps
};


export const InReportContext: Story = {
  args: defaultProps,
  decorators: [
    (Story) => (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto p-6">
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-6 border-b">
              <h1 className="text-2xl font-bold text-gray-900">Balance de Masas</h1>
              <p className="text-gray-600 mt-1">Reporte de balance de masas por período</p>
            </div>
            <div className="p-6">
              <Story />
            </div>
            <div className="p-6 border-t bg-gray-50">
              <p className="text-sm text-gray-500">
                Los filtros se aplicarán automáticamente al seleccionar las fechas.
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  ],
  parameters: {
    docs: {
      description: {
        story: `
          Shows the filter component in a typical report page context with proper styling and layout.
        `
      }
    }
  }
};


export const Compact: Story = {
  args: defaultProps,
  decorators: [
    (Story) => (
      <div className="max-w-md mx-auto p-4 bg-white border rounded">
        <Story />
      </div>
    )
  ],
  parameters: {
    docs: {
      description: {
        story: `
          Compact version of the filters suitable for smaller containers or sidebar usage.
        `
      }
    }
  }
};