import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import PrintToPDFButton from './PrintToPDFButton';
import PrintPreviewDisplay from './PrintPreviewDisplay';
import { TableData } from '../models/table.type';


const mockTableData: TableData = {
  massBalance: [
    {
      nuap: "NUAP Test 001",
      serviceArea: "Área de Prestación Norte",
      urbanCleaningTons: "150.25",
      sweepingTons: "75.50",
      nonRecyclableTons: "120.75",
      rejectionTons: "30.25",
      recyclableTons: "45.00",
      totalTons: "421.75",
      totalByNuap: "450.00",
      discountTons: "25.50",
      totalByNuapDiscounts: "424.50",
      difference: "2.75"
    },
    {
      nuap: "NUAP Test 002",
      serviceArea: "Área de Prestación Sur",
      urbanCleaningTons: "200.00",
      sweepingTons: "100.25",
      nonRecyclableTons: "180.50",
      rejectionTons: "40.75",
      recyclableTons: "60.00",
      totalTons: "581.50",
      totalByNuap: "600.00",
      discountTons: "35.25",
      totalByNuapDiscounts: "564.75",
      difference: "-16.75"
    }
  ],
  massBalanceTotals: {
    allUrbanCleaningTons: "350.25",
    allSweepingTons: "175.75",
    allNonRecyclableTons: "301.25",
    allRejectionTons: "71.00",
    allRecyclableTons: "105.00",
    totalTonsF14: "1003.25",
    allDiscountTons: "60.75",
    allTotalsMinusDiscounts: "989.25",
    totalTonsF34: "1050.00",
    totalDifference: "-14.00"
  },
  finalDispositionTotals: {
    total: "1050.00",
    emvariasTotal: "989.25"
  },
  distributions: [
    {
      serviceArea: "Área de Prestación Norte",
      tons: "450.00",
      trips: "45",
      tonPerTrip: "10.00",
      tollSharedRouteTons: "225.00",
      tollPercentage: "50.0",
      compensationTons: "22.50"
    },
    {
      serviceArea: "Área de Prestación Sur",
      tons: "600.00",
      trips: "60",
      tonPerTrip: "10.00",
      tollSharedRouteTons: "300.00",
      tollPercentage: "50.0",
      compensationTons: "30.00"
    }
  ],
  isValid: true
};

const meta: Meta<typeof PrintToPDFButton> = {
  title: 'MassBalance/Molecule/PrintToPDFButton',
  component: PrintToPDFButton,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
          **PrintToPDFButton Component**
          
          A specialized button component for generating PDF prints of Mass Balance reports.
          
          **Features:**
          - Conditional rendering based on data availability
          - Loading state management during print preparation
          - Professional black and white styling optimized for printing
          - Real data integration replacing placeholder values
          - Tailwind CSS styling for consistent design
          - Error handling with user-friendly feedback
          - Popup window management for print functionality
          
          **Usage:**
          This component is integrated into the Mass Balance report page and becomes available
          when valid report data is loaded. It opens a new window with a print-optimized
          layout that includes all Mass Balance tables with proper formatting.
        `
      }
    }
  },
  tags: ['autodocs'],
  argTypes: {
    data: {
      description: 'Mass Balance data object containing all report information',
      control: { type: 'object' }
    },
    disabled: {
      description: 'Whether the button should be disabled',
      control: { type: 'boolean' }
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    data: mockTableData,
    disabled: false
  }
};

export const Disabled: Story = {
  args: {
    data: null,
    disabled: true
  }
};

export const DisabledWithData: Story = {
  args: {
    data: mockTableData,
    disabled: true
  }
};

export const MinimalData: Story = {
  args: {
    data: {
      massBalance: [
        {
          nuap: "NUAP Centro",
          serviceArea: "Área Centro",
          urbanCleaningTons: "25.50",
          sweepingTons: "12.25",
          nonRecyclableTons: "18.75",
          rejectionTons: "4.50",
          recyclableTons: "8.25",
          totalTons: "69.25",
          totalByNuap: "72.00",
          discountTons: "2.75",
          totalByNuapDiscounts: "69.25",
          difference: "0.00"
        },
        {
          nuap: "NUAP Oriente",
          serviceArea: "Área Oriente",
          urbanCleaningTons: "18.75",
          sweepingTons: "9.50",
          nonRecyclableTons: "14.25",
          rejectionTons: "3.25",
          recyclableTons: "6.00",
          totalTons: "51.75",
          totalByNuap: "54.00",
          discountTons: "2.25",
          totalByNuapDiscounts: "51.75",
          difference: "0.00"
        },
        {
          nuap: "NUAP Occidente",
          serviceArea: "Área Occidente",
          urbanCleaningTons: "22.00",
          sweepingTons: "11.00",
          nonRecyclableTons: "16.50",
          rejectionTons: "4.00",
          recyclableTons: "7.25",
          totalTons: "60.75",
          totalByNuap: "63.00",
          discountTons: "2.25",
          totalByNuapDiscounts: "60.75",
          difference: "0.00"
        },
        {
          nuap: "NUAP Norte",
          serviceArea: "Área Norte",
          urbanCleaningTons: "30.25",
          sweepingTons: "15.00",
          nonRecyclableTons: "22.50",
          rejectionTons: "5.75",
          recyclableTons: "10.50",
          totalTons: "84.00",
          totalByNuap: "87.00",
          discountTons: "3.00",
          totalByNuapDiscounts: "84.00",
          difference: "0.00"
        },
        {
          nuap: "NUAP Sur",
          serviceArea: "Área Sur",
          urbanCleaningTons: "28.50",
          sweepingTons: "14.25",
          nonRecyclableTons: "21.00",
          rejectionTons: "5.25",
          recyclableTons: "9.75",
          totalTons: "78.75",
          totalByNuap: "81.50",
          discountTons: "2.75",
          totalByNuapDiscounts: "78.75",
          difference: "0.00"
        }
      ],
      massBalanceTotals: {
        allUrbanCleaningTons: "125.00",
        allSweepingTons: "62.00",
        allNonRecyclableTons: "93.00",
        allRejectionTons: "22.75",
        allRecyclableTons: "41.75",
        totalTonsF14: "344.50",
        allDiscountTons: "13.00",
        allTotalsMinusDiscounts: "344.50",
        totalTonsF34: "357.50",
        totalDifference: "0.00"
      },
      finalDispositionTotals: {
        total: "357.50",
        emvariasTotal: "344.50"
      },
      distributions: [
        {
          serviceArea: "Área Centro",
          tons: "72.00",
          trips: "7",
          tonPerTrip: "10.29",
          tollSharedRouteTons: "36.00",
          tollPercentage: "50.0",
          compensationTons: "3.60"
        },
        {
          serviceArea: "Área Oriente",
          tons: "54.00",
          trips: "5",
          tonPerTrip: "10.80",
          tollSharedRouteTons: "27.00",
          tollPercentage: "50.0",
          compensationTons: "2.70"
        },
        {
          serviceArea: "Área Occidente",
          tons: "63.00",
          trips: "6",
          tonPerTrip: "10.50",
          tollSharedRouteTons: "31.50",
          tollPercentage: "50.0",
          compensationTons: "3.15"
        },
        {
          serviceArea: "Área Norte",
          tons: "87.00",
          trips: "9",
          tonPerTrip: "9.67",
          tollSharedRouteTons: "43.50",
          tollPercentage: "50.0",
          compensationTons: "4.35"
        },
        {
          serviceArea: "Área Sur",
          tons: "81.50",
          trips: "8",
          tonPerTrip: "10.19",
          tollSharedRouteTons: "40.75",
          tollPercentage: "50.0",
          compensationTons: "4.08"
        }
      ],
      isValid: true
    },
    disabled: false
  }
};

export const MinimalDataPopupPreview: Story = {
  render: () => {
    const minimalData = {
      massBalance: [
        {
          nuap: "NUAP Centro",
          serviceArea: "Área Centro",
          urbanCleaningTons: "25.50",
          sweepingTons: "12.25",
          nonRecyclableTons: "18.75",
          rejectionTons: "4.50",
          recyclableTons: "8.25",
          totalTons: "69.25",
          totalByNuap: "72.00",
          discountTons: "2.75",
          totalByNuapDiscounts: "69.25",
          difference: "0.00"
        },
        {
          nuap: "NUAP Oriente",
          serviceArea: "Área Oriente",
          urbanCleaningTons: "18.75",
          sweepingTons: "9.50",
          nonRecyclableTons: "14.25",
          rejectionTons: "3.25",
          recyclableTons: "6.00",
          totalTons: "51.75",
          totalByNuap: "54.00",
          discountTons: "2.25",
          totalByNuapDiscounts: "51.75",
          difference: "0.00"
        },
        {
          nuap: "NUAP Occidente",
          serviceArea: "Área Occidente",
          urbanCleaningTons: "22.00",
          sweepingTons: "11.00",
          nonRecyclableTons: "16.50",
          rejectionTons: "4.00",
          recyclableTons: "7.25",
          totalTons: "60.75",
          totalByNuap: "63.00",
          discountTons: "2.25",
          totalByNuapDiscounts: "60.75",
          difference: "0.00"
        },
        {
          nuap: "NUAP Norte",
          serviceArea: "Área Norte",
          urbanCleaningTons: "30.25",
          sweepingTons: "15.00",
          nonRecyclableTons: "22.50",
          rejectionTons: "5.75",
          recyclableTons: "10.50",
          totalTons: "84.00",
          totalByNuap: "87.00",
          discountTons: "3.00",
          totalByNuapDiscounts: "84.00",
          difference: "0.00"
        },
        {
          nuap: "NUAP Sur",
          serviceArea: "Área Sur",
          urbanCleaningTons: "28.50",
          sweepingTons: "14.25",
          nonRecyclableTons: "21.00",
          rejectionTons: "5.25",
          recyclableTons: "9.75",
          totalTons: "78.75",
          totalByNuap: "81.50",
          discountTons: "2.75",
          totalByNuapDiscounts: "78.75",
          difference: "0.00"
        }
      ],
      massBalanceTotals: {
        allUrbanCleaningTons: "125.00",
        allSweepingTons: "62.00",
        allNonRecyclableTons: "93.00",
        allRejectionTons: "22.75",
        allRecyclableTons: "41.75",
        totalTonsF14: "344.50",
        allDiscountTons: "13.00",
        allTotalsMinusDiscounts: "344.50",
        totalTonsF34: "357.50",
        totalDifference: "0.00"
      },
      finalDispositionTotals: {
        total: "357.50",
        emvariasTotal: "344.50"
      },
      distributions: [
        {
          serviceArea: "Área Centro",
          tons: "72.00",
          trips: "7",
          tonPerTrip: "10.29",
          tollSharedRouteTons: "36.00",
          tollPercentage: "50.0",
          compensationTons: "3.60"
        },
        {
          serviceArea: "Área Oriente",
          tons: "54.00",
          trips: "5",
          tonPerTrip: "10.80",
          tollSharedRouteTons: "27.00",
          tollPercentage: "50.0",
          compensationTons: "2.70"
        },
        {
          serviceArea: "Área Occidente",
          tons: "63.00",
          trips: "6",
          tonPerTrip: "10.50",
          tollSharedRouteTons: "31.50",
          tollPercentage: "50.0",
          compensationTons: "3.15"
        },
        {
          serviceArea: "Área Norte",
          tons: "87.00",
          trips: "9",
          tonPerTrip: "9.67",
          tollSharedRouteTons: "43.50",
          tollPercentage: "50.0",
          compensationTons: "4.35"
        },
        {
          serviceArea: "Área Sur",
          tons: "81.50",
          trips: "8",
          tonPerTrip: "10.19",
          tollSharedRouteTons: "40.75",
          tollPercentage: "50.0",
          compensationTons: "4.08"
        }
      ],
      isValid: true
    };

    return <PrintPreviewDisplay data={minimalData} period="2024/11" />;
  },
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        story: `
          This story shows the print preview popup content directly without requiring user interaction.
          It displays the same minimal data as the MinimalData story but renders the complete
          print-optimized HTML layout in an iframe, including:
          
          - Generated timestamp header
          - Mass Balance table with all data
          - Final Disposition totals
          - Distribution table
          - Print-optimized styling (white background, black text)
          
          This is useful for documentation, testing, and visual verification of the print layout.
        `
      }
    }
  }
};