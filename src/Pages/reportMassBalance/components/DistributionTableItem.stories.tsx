import type { Meta, StoryObj } from '@storybook/react';
import DistributionTableItem from './DistributionTableItem';
import { DistributionRow } from '../models';


const mockDistributionRow: DistributionRow = {
  serviceArea: "Área de Prestación Norte",
  tons: "450.00",
  trips: "45",
  tonPerTrip: "10.00",
  tollSharedRouteTons: "225.00",
  tollPercentage: "50.0",
  compensationTons: "22.50"
};

const mockHighVolumeRow: DistributionRow = {
  serviceArea: "Área de Prestación Central",
  tons: "1250.75",
  trips: "125",
  tonPerTrip: "10.01",
  tollSharedRouteTons: "625.38",
  tollPercentage: "50.0",
  compensationTons: "62.54"
};


const meta: Meta<typeof DistributionTableItem> = {
  title: 'MassBalance/Atom/DistributionTableItem',
  component: DistributionTableItem,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
          **DistributionTableItem Component**
          
          A table row component that displays distribution data for waste management areas.
          
          **Features:**
          - Shows tonnage distribution across service areas
          - Displays trip calculations and efficiency metrics
          - Shows toll and compensation calculations
          - Responsive table styling with alternating row colors
          - Proper data formatting and alignment
        `
      }
    }
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <table className="border border-gray-300 border-separate border-spacing-0">
        <thead>
          <tr className="bg-gray-100">
            <th className="p-2 border-b">Área de Prestación</th>
            <th className="p-2 border-b">Toneladas</th>
            <th className="p-2 border-b">Viajes</th>
            <th className="p-2 border-b">Ton/Viaje</th>
            <th className="p-2 border-b">Ton Peaje Ruta Compartida</th>
            <th className="p-2 border-b">% Distpeaje</th>
            <th className="p-2 border-b">Toneladas a Compensar</th>
          </tr>
        </thead>
        <tbody>
          <Story />
        </tbody>
      </table>
    )
  ],
  argTypes: {
    item: {
      description: 'Distribution data for a single service area',
      control: { type: 'object' }
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    item: mockDistributionRow
  }
};

export const HighVolume: Story = {
  args: {
    item: mockHighVolumeRow
  }
};

export const LowEfficiency: Story = {
  args: {
    item: {
      serviceArea: "Área de Prestación Rural",
      tons: "180.50",
      trips: "25",
      tonPerTrip: "7.22",
      tollSharedRouteTons: "90.25",
      tollPercentage: "50.0",
      compensationTons: "9.03"
    }
  }
};