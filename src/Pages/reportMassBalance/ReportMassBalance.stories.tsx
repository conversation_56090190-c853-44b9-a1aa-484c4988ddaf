import type { Meta, StoryObj } from '@storybook/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ReportMassBalance from './index';


const mockQueryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      staleTime: Infinity,
      refetchOnWindowFocus: false,
    },
  },
});




const meta: Meta<typeof ReportMassBalance> = {
  title: 'MassBalance/Page/ReportMassBalance',
  component: ReportMassBalance,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: `
          **ReportMassBalance Page Component**
          
          The complete Mass Balance report page that integrates all components into a cohesive reporting interface.
          
          **Features:**
          - Complete report layout with header, filters, and data tables
          - Mass balance calculations and validation
          - Distribution analysis and compensation calculations
          - Export functionality (PDF, Excel, SUI generation)
          - Real-time data filtering and state management
          - Responsive design with horizontal scrolling for large tables
          - Loading states and error handling
          - Integration with backend APIs
          
          **Architecture:**
          - Uses Redux for global state management
          - React Query for data fetching and caching
          - Modular component composition
          - Atomic Design principles implementation
          - Material-UI integration for consistent styling
          
          **Data Flow:**
          1. User selects date filters
          2. API fetches mass balance data
          3. Data is processed and validated
          4. Tables are rendered with calculated values
          5. Export options become available
          6. User can generate reports in various formats
        `
      }
    }
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <QueryClientProvider client={mockQueryClient}>
        <div className="min-h-screen bg-gray-50">
          <Story />
        </div>
      </QueryClientProvider>
    )
  ]
};

export default meta;
type Story = StoryObj<typeof meta>;


export const Default: Story = {
  parameters: {
    docs: {
      description: {
        story: `
          The default state of the Mass Balance report page.
          Shows the complete interface with all components integrated.
        `
      }
    }
  }
};


export const LoadingState: Story = {
  parameters: {
    docs: {
      description: {
        story: `
          Shows the page in a loading state while data is being fetched.
          Displays skeleton loaders and loading indicators.
        `
      }
    }
  }
};


export const MobileView: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    },
    docs: {
      description: {
        story: `
          Shows how the Mass Balance report adapts to mobile screen sizes.
          Tables become horizontally scrollable to maintain usability.
        `
      }
    }
  }
};


export const TabletView: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'tablet'
    },
    docs: {
      description: {
        story: `
          Shows the Mass Balance report on tablet-sized screens.
          Layout adjusts to provide optimal viewing experience.
        `
      }
    }
  }
};