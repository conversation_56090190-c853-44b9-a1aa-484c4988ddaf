import type { <PERSON>a, <PERSON>Obj } from '@storybook/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const mockQueryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      staleTime: Infinity,
      refetchOnWindowFocus: false,
    },
  },
});

const mockWrapper = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider client={mockQueryClient}>
    {children}
  </QueryClientProvider>
);

const meta: Meta = {
  title: 'ReportWeighins/Hook/useReport',
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
          **useReport Hook**
          
          A custom React hook that manages the data fetching and state for the Weighins report functionality.
          
          **Features:**
          - Paginated data fetching for weighing records
          - Integration with URL-based query parameters
          - Automatic data formatting and transformation
          - Loading and error state management
          - Integration with global URL store
          
          **Parameters:**
          - \`pageNumber\`: Current page number for pagination
          
          **Returns:**
          - \`table.responseWeighinsReport\`: Query response with loading/error states
          - \`table.dataTable\`: Formatted table data with pagination info
          - \`table.dataTableFormat\`: Local state for formatted data
          - \`table.setDataTableFormat\`: Function to update formatted data
          - \`table.data\`: Raw response data from API
          
          **Architecture:**
          - Uses React Query for data fetching and caching
          - Integrates with Zustand store for URL management
          - Provides consistent data structure for table components
          - Handles pagination logic automatically
          
          **Usage Example:**
          \`\`\`tsx
          const { table } = useReport(1);
          const { responseWeighinsReport, dataTable } = table;
          
          if (responseWeighinsReport.isLoading) {
            return <LoadingSkeleton />;
          }
          
          if (responseWeighinsReport.isError) {
            return <ErrorMessage />;
          }
          
          return <DataTable data={dataTable.results} />;
          \`\`\`
        `
      }
    }
  },
  tags: ['autodocs']
};

export default meta;
type Story = StoryObj<typeof meta>;

export const HookDocumentation: Story = {
  render: () => (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h2 className="text-xl font-bold text-blue-900 mb-3">useReport Hook</h2>
        <p className="text-blue-800 mb-4">
          Hook personalizado para gestionar los datos y estado del reporte de pesajes.
        </p>
        
        <div className="space-y-4">
          <div>
            <h3 className="font-semibold text-blue-900 mb-2">Características principales:</h3>
            <ul className="space-y-1 text-blue-800 text-sm">
              <li>• Obtención paginada de datos de pesajes</li>
              <li>• Integración con parámetros de URL</li>
              <li>• Formateo automático de datos</li>
              <li>• Gestión de estados de carga y error</li>
              <li>• Integración con store global de URL</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold text-blue-900 mb-2">Valor retornado:</h3>
            <div className="bg-white p-4 rounded border font-mono text-sm">
              <div className="text-gray-700">
                <div><span className="text-purple-600">table</span>: &#123;</div>
                <div className="ml-4"><span className="text-blue-600">responseWeighinsReport</span>: QueryResult,</div>
                <div className="ml-4"><span className="text-blue-600">dataTable</span>: FormattedData,</div>
                <div className="ml-4"><span className="text-blue-600">dataTableFormat</span>: State,</div>
                <div className="ml-4"><span className="text-blue-600">setDataTableFormat</span>: Function,</div>
                <div className="ml-4"><span className="text-blue-600">data</span>: RawResponse</div>
                <div>&#125;</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="font-semibold text-green-900 mb-2">Estados de carga</h3>
          <ul className="space-y-1 text-green-800 text-sm">
            <li>• isLoading - Datos cargando</li>
            <li>• isError - Error en la petición</li>
            <li>• isSuccess - Datos cargados</li>
            <li>• isFetching - Actualizando datos</li>
          </ul>
        </div>
        
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
          <h3 className="font-semibold text-amber-900 mb-2">Integración</h3>
          <ul className="space-y-1 text-amber-800 text-sm">
            <li>• React Query para caché</li>
            <li>• Zustand para estado global</li>
            <li>• Paginación automática</li>
            <li>• Filtros por URL</li>
          </ul>
        </div>
      </div>
      
      <div className="bg-gray-50 border rounded-lg p-6">
        <h3 className="font-semibold text-gray-900 mb-3">Ejemplo de uso</h3>
        <pre className="bg-gray-800 text-green-400 p-4 rounded overflow-x-auto text-sm">
{`const ReportComponent = () => {
  const [pageNumber, setPageNumber] = useState(1);
  const { table } = useReport(pageNumber);
  const { responseWeighinsReport, dataTable } = table;

  if (responseWeighinsReport.isLoading) {
    return <TableSkeleton />;
  }

  if (responseWeighinsReport.isError) {
    return <ErrorMessage />;
  }

  return (
    <div>
      <DataTable data={dataTable.results} />
      <Pagination 
        current={pageNumber}
        total={dataTable.totalRecords}
        onChange={setPageNumber}
      />
    </div>
  );
};`}
        </pre>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: `
          Comprehensive documentation for the useReport hook showing its usage, 
          return values, and integration patterns within the weighins report system.
        `
      }
    }
  }
};

export const UsageExample: Story = {
  render: () => (
    <div className="max-w-3xl mx-auto p-6">
      <h2 className="text-2xl font-bold mb-6">Implementación del Hook useReport</h2>
      
      <div className="space-y-6">
        <div className="bg-white border rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">1. Importación y configuración</h3>
          <pre className="bg-gray-100 p-4 rounded overflow-x-auto">
{`import { useReport } from '@/Pages/reportWeighins/hook/useReport';

const WeighinsReport = () => {
  const [pageNumber, setPageNumber] = useState(1);
  const { table } = useReport(pageNumber);
}`}
          </pre>
        </div>
        
        <div className="bg-white border rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">2. Manejo de estados</h3>
          <pre className="bg-gray-100 p-4 rounded overflow-x-auto">
{`const { responseWeighinsReport, dataTable, data } = table;

// Estados disponibles
const isLoading = responseWeighinsReport.isLoading;
const isError = responseWeighinsReport.isError;
const error = responseWeighinsReport.error;`}
          </pre>
        </div>
        
        <div className="bg-white border rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">3. Renderizado condicional</h3>
          <pre className="bg-gray-100 p-4 rounded overflow-x-auto">
{`if (responseWeighinsReport.isError) {
  return <ErrorInformation messageError={error?.response?.data.Messages} />;
}

if (responseWeighinsReport.isFetching) {
  return <TableSkeleton />;
}

return <DataTable data={dataTable.results} />;`}
          </pre>
        </div>
        
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-semibold text-blue-900 mb-2">💡 Características clave</h4>
          <ul className="space-y-1 text-blue-800 text-sm">
            <li>• Automatiza la paginación de resultados</li>
            <li>• Integra filtros a través de URL</li>
            <li>• Mantiene estado consistente entre renders</li>
            <li>• Optimiza las peticiones con React Query</li>
          </ul>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: `
          Practical implementation example showing how to use the useReport hook 
          in a real component with proper error handling and state management.
        `
      }
    }
  }
};
