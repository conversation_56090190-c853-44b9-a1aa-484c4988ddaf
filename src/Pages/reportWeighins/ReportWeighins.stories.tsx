import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { MemoryRouter } from 'react-router-dom';
import ReportWeighins from './index';

const mockQueryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      staleTime: Infinity,
      refetchOnWindowFocus: false,
    },
  },
});

const meta: Meta<typeof ReportWeighins> = {
  title: 'ReportWeighins/Page/ReportWeighins',
  component: ReportWeighins,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: `
          **ReportWeighins Page Component**
          
          The complete Weighins report page that provides a comprehensive interface for viewing and filtering weighing scale records.
          
          **Features:**
          - Complete weighing scale report layout with header, filters, and data tables
          - Advanced filtering by date range, license plate, ticket number, and company
          - Data export functionality (Excel, PDF)
          - Real-time data filtering and pagination
          - Responsive design with loading states and error handling
          - Integration with backend APIs for weighing data
          
          **Architecture:**
          - Uses Redux for global state management
          - React Query for data fetching and caching
          - Modular component composition following Atomic Design principles
          - Material-UI integration for consistent styling
          - Integration with global filter store
          
          **Data Flow:**
          1. User applies filters through the filter drawer
          2. API fetches weighing data based on filter criteria
          3. Data is displayed in a paginated table format
          4. Users can export filtered data or view detailed records
          
          **Components Included:**
          - Header with title and filter drawer trigger
          - FiltersWeighins component in a drawer
          - TableMasterV2 for data display
          - Pagination controls
          - Loading skeletons and error states
        `
      }
    }
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <QueryClientProvider client={mockQueryClient}>
        <MemoryRouter>
          <div className="min-h-screen bg-gray-50">
            <Story />
          </div>
        </MemoryRouter>
      </QueryClientProvider>
    )
  ]
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  parameters: {
    docs: {
      description: {
        story: `
          Default view of the Weighins report page with all functionality enabled.
          Shows the complete interface including header, filters, and data table.
        `
      }
    }
  }
};

export const WithMockData: Story = {
  parameters: {
    mockData: [
      {
        url: '**/weighins**',
        method: 'GET',
        status: 200,
        response: {
          data: {
            results: [
              {
                id: 'WGH001',
                licensePlate: 'ABC123',
                nit: 123456789,
                arrivingWeight: 1500.5,
                leavingWeight: 800.0,
                depositWeight: 700.5,
                entryDate: '2024-01-15T08:30:00',
                egressDate: '2024-01-15T14:45:00',
                materialType: 'Mineral de oro',
                depositPlace: 1,
                originType: 'Mina',
                nuap: 123,
                loadingDate: '2024-01-15T09:00:00',
                cancelDate: null,
                loadingType: 'Carga completa',
                companyName: 'Minería El Dorado S.A.S.',
                town: {
                  code: '05001',
                  name: 'Medellín',
                  department: 'Antioquia',
                  province: 'Valle de Aburrá'
                }
              },
              {
                id: 'WGH002',
                licensePlate: 'XYZ789',
                nit: *********,
                arrivingWeight: 2000.0,
                leavingWeight: 1200.5,
                depositWeight: 799.5,
                entryDate: '2024-01-15T10:15:00',
                egressDate: '2024-01-15T16:30:00',
                materialType: 'Concentrado',
                depositPlace: 2,
                originType: 'Planta',
                nuap: 456,
                loadingDate: '2024-01-15T10:45:00',
                cancelDate: null,
                loadingType: 'Carga parcial',
                companyName: 'Procesadora Andina Ltda.',
                town: {
                  code: '05002',
                  name: 'Barbosa',
                  department: 'Antioquia',
                  province: 'Valle de Aburrá'
                }
              }
            ],
            totalRecords: 25
          }
        }
      }
    ],
    docs: {
      description: {
        story: `
          Example with mock data showing how the weighins report displays actual weighing records.
          Includes sample data with different vehicle types, companies, and weighing measurements.
        `
      }
    }
  }
};

export const LoadingState: Story = {
  parameters: {
    mockData: [
      {
        url: '**/weighins**',
        method: 'GET',
        delay: 10000, // Long delay to show loading state
        status: 200,
        response: { data: { results: [], totalRecords: 0 } }
      }
    ],
    docs: {
      description: {
        story: `
          Shows the loading state with skeleton components while data is being fetched.
          Demonstrates the user experience during API calls.
        `
      }
    }
  }
};

export const ErrorState: Story = {
  parameters: {
    mockData: [
      {
        url: '**/weighins**',
        method: 'GET',
        status: 500,
        response: {
          Messages: ['Error interno del servidor. No se pudieron cargar los datos de pesajes.']
        }
      }
    ],
    docs: {
      description: {
        story: `
          Demonstrates the error handling when the API request fails.
          Shows error message component with appropriate user feedback.
        `
      }
    }
  }
};

export const EmptyResults: Story = {
  parameters: {
    mockData: [
      {
        url: '**/weighins**',
        method: 'GET',
        status: 200,
        response: {
          data: {
            results: [],
            totalRecords: 0
          }
        }
      }
    ],
    docs: {
      description: {
        story: `
          Shows the empty state when no weighing records match the current filters.
          Displays the NoResults component with appropriate messaging.
        `
      }
    }
  }
};

export const ResponsiveView: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    },
    docs: {
      description: {
        story: `
          Demonstrates how the weighins report adapts to mobile and tablet viewports.
          Shows responsive behavior of the table and filter drawer.
        `
      }
    }
  }
};
