import type { Meta, StoryObj } from '@storybook/react';

const meta: Meta = {
    title: 'ReportWeighins/Overview',
    parameters: {
        layout: 'fullscreen',
        docs: {
            description: {
                component: `
          # Módulo Report Weighins - Documentación Completa
          
          Este módulo proporciona una interfaz completa para la gestión y visualización de reportes de pesajes en el sistema Mercury.
          
          ## 📁 Estructura del Módulo
          
          El módulo está organizado siguiendo principios de Atomic Design y arquitectura modular:
          
          ### 🎯 Componentes Principales
          
          #### **Pages/Organisms**
          - **ReportWeighins** - Página principal del reporte con tabla paginada y filtros
          - **FiltersWeighins** - Organismo de filtros completo en drawer lateral
          
          #### **Molecules**
          - **LicensePlateFilter** - Filtro específico para placas de vehículos
          - **TicketNumberFilter** - Filtro para números de ticket de pesaje
          - **CompanyNameFilter** - Filtro de empresas con integración NIT
          
          #### **Hooks**
          - **useReport** - Hook personalizado para gestión de datos y estado
          
          ## 🔧 Funcionalidades Principales
          
          ### **Filtrado Avanzado**
          - Filtro por rango de fechas con validación
          - Búsqueda por placa de vehículo con autocompletado
          - Selección por número de ticket único
          - Filtro por empresa con validación NIT
          - Persistencia de filtros en localStorage
          
          ### **Visualización de Datos**
          - Tabla paginada con 25 registros por página
          - Estados de carga con skeletons
          - Manejo de errores con mensajes informativos
          - Vista de resultados vacíos
          - Diseño responsivo para mobile y desktop
          
          ### **Exportación**
          - Exportación a Excel con filtros aplicados
          - Generación de reportes PDF
          - Opciones de descarga personalizables
          
          ## 🏗️ Arquitectura Técnica
          
          ### **Estado Global**
          - **Redux** para gestión de filtros globales
          - **Zustand** para estado específico del módulo
          - **React Query** para cache y sincronización de datos
          
          ### **Integración API**
          - Endpoints RESTful para datos de pesajes
          - Integración con servicio de NITs
          - Paginación server-side
          - Filtros dinámicos por URL
          
          ### **Tipos de Datos**
          
          \`\`\`typescript
          type ReportWeighins = {
            id: string;                    // ID único del pesaje
            licensePlate: string;          // Placa del vehículo
            nit: number;                   // NIT de la empresa
            arrivingWeight: number;        // Peso de llegada
            leavingWeight: number;         // Peso de salida
            depositWeight: number;         // Peso depositado
            entryDate: string;             // Fecha de entrada
            egressDate: string;            // Fecha de salida
            materialType: string;          // Tipo de material
            depositPlace: number;          // Lugar de depósito
            originType: string;            // Tipo de origen
            nuap: number;                  // Número NUAP
            loadingDate: string;           // Fecha de carga
            cancelDate: string;            // Fecha de cancelación
            loadingType: string;           // Tipo de carga
            companyName: string;           // Nombre de la empresa
            town: ReportWeighinsTown;      // Información del municipio
          };
          \`\`\`
          
          ## 📊 Casos de Uso
          
          ### **Operadores de Pesaje**
          - Consulta de registros históricos
          - Verificación de transacciones
          - Generación de reportes diarios
          
          ### **Supervisores**
          - Análisis de tendencias de pesaje
          - Auditoría de operaciones
          - Control de calidad de datos
          
          ### **Administradores**
          - Reportes ejecutivos
          - Análisis por empresa/período
          - Exportación masiva de datos
          
          ## 🎨 Patrones de Diseño
          
          ### **Atomic Design**
          - Atoms: Inputs básicos, botones, labels
          - Molecules: Filtros individuales
          - Organisms: Panel de filtros completo
          - Templates: Layout de página
          - Pages: Vista completa del reporte
          
          ### **Composition Pattern**
          - Componentes altamente reutilizables
          - Props drilling mínimo
          - Separación clara de responsabilidades
          
          ## 🔍 Testing Strategy
          
          Cada componente incluye stories de Storybook que cubren:
          - Estados por defecto
          - Estados de carga y error
          - Interacciones del usuario
          - Casos edge y límite
          - Responsive design
          
          ## 📱 Responsive Design
          
          - **Desktop**: Vista completa con sidebar de filtros
          - **Tablet**: Drawer modal para filtros
          - **Mobile**: Interface optimizada touch-first
          
          ## 🚀 Performance
          
          - Lazy loading de componentes
          - Memoización de cálculos pesados
          - Virtualización de listas largas
          - Debounce en búsquedas
          - Cache inteligente con React Query
        `
            }
        }
    },
    tags: ['autodocs']
};

export default meta;
type Story = StoryObj<typeof meta>;

export const ModuleOverview: Story = {
    render: () => (
        <div className="max-w-6xl mx-auto p-8 space-y-8">
            <div className="text-center">
                <h1 className="text-4xl font-bold text-gray-900 mb-4">📊 Report Weighins</h1>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                    Sistema completo de reportes de pesajes con filtrado avanzado,
                    exportación de datos y visualización responsiva.
                </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <div className="text-3xl mb-3">🎯</div>
                    <h3 className="text-lg font-semibold text-blue-900 mb-2">Componentes</h3>
                    <p className="text-blue-800 text-sm">
                        Más de 5 componentes documentados siguiendo principios de Atomic Design
                    </p>
                </div>

                <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                    <div className="text-3xl mb-3">🔧</div>
                    <h3 className="text-lg font-semibold text-green-900 mb-2">Funcionalidades</h3>
                    <p className="text-green-800 text-sm">
                        Filtrado avanzado, paginación, exportación y manejo de estados
                    </p>
                </div>

                <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
                    <div className="text-3xl mb-3">🏗️</div>
                    <h3 className="text-lg font-semibold text-purple-900 mb-2">Arquitectura</h3>
                    <p className="text-purple-800 text-sm">
                        Redux, React Query, TypeScript y patrones modernos de React
                    </p>
                </div>
            </div>

            <div className="bg-white border rounded-lg p-6">
                <h2 className="text-2xl font-bold mb-4">📚 Stories Disponibles</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                        <div className="p-3 bg-gray-50 rounded">
                            <h4 className="font-semibold">ReportWeighins/Page/ReportWeighins</h4>
                            <p className="text-sm text-gray-600">Página principal completa</p>
                        </div>
                        <div className="p-3 bg-gray-50 rounded">
                            <h4 className="font-semibold">ReportWeighins/Organism/FiltersWeighins</h4>
                            <p className="text-sm text-gray-600">Panel de filtros completo</p>
                        </div>
                        <div className="p-3 bg-gray-50 rounded">
                            <h4 className="font-semibold">ReportWeighins/Molecule/LicensePlateFilter</h4>
                            <p className="text-sm text-gray-600">Filtro de placas</p>
                        </div>
                    </div>
                    <div className="space-y-3">
                        <div className="p-3 bg-gray-50 rounded">
                            <h4 className="font-semibold">ReportWeighins/Molecule/TicketNumberFilter</h4>
                            <p className="text-sm text-gray-600">Filtro de tickets</p>
                        </div>
                        <div className="p-3 bg-gray-50 rounded">
                            <h4 className="font-semibold">ReportWeighins/Molecule/CompanyNameFilter</h4>
                            <p className="text-sm text-gray-600">Filtro de empresas</p>
                        </div>
                        <div className="p-3 bg-gray-50 rounded">
                            <h4 className="font-semibold">ReportWeighins/Hook/useReport</h4>
                            <p className="text-sm text-gray-600">Hook de gestión de datos</p>
                        </div>
                    </div>
                </div>
            </div>

            <div className="bg-gradient-to-r from-blue-50 to-purple-50 border rounded-lg p-6">
                <h2 className="text-2xl font-bold mb-4">🎯 Navegación Rápida</h2>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    <button className="p-3 bg-white rounded border hover:bg-gray-50 text-left">
                        <div className="font-medium">Página Principal</div>
                        <div className="text-sm text-gray-500">ReportWeighins</div>
                    </button>
                    <button className="p-3 bg-white rounded border hover:bg-gray-50 text-left">
                        <div className="font-medium">Filtros</div>
                        <div className="text-sm text-gray-500">FiltersWeighins</div>
                    </button>
                    <button className="p-3 bg-white rounded border hover:bg-gray-50 text-left">
                        <div className="font-medium">Placas</div>
                        <div className="text-sm text-gray-500">LicensePlateFilter</div>
                    </button>
                    <button className="p-3 bg-white rounded border hover:bg-gray-50 text-left">
                        <div className="font-medium">Tickets</div>
                        <div className="text-sm text-gray-500">TicketNumberFilter</div>
                    </button>
                    <button className="p-3 bg-white rounded border hover:bg-gray-50 text-left">
                        <div className="font-medium">Empresas</div>
                        <div className="text-sm text-gray-500">CompanyNameFilter</div>
                    </button>
                    <button className="p-3 bg-white rounded border hover:bg-gray-50 text-left">
                        <div className="font-medium">Hook</div>
                        <div className="text-sm text-gray-500">useReport</div>
                    </button>
                </div>
            </div>
        </div>
    ),
    parameters: {
        docs: {
            description: {
                story: `
          Vista general completa del módulo Report Weighins mostrando todos los componentes,
          funcionalidades y arquitectura del sistema de reportes de pesajes.
        `
            }
        }
    }
};

export const ComponentHierarchy: Story = {
    render: () => (
        <div className="max-w-4xl mx-auto p-8">
            <h2 className="text-3xl font-bold mb-8 text-center">🏗️ Jerarquía de Componentes</h2>

            <div className="space-y-8">
                <div className="bg-blue-50 border-2 border-blue-200 rounded-lg p-6">
                    <h3 className="text-xl font-bold text-blue-900 mb-4">📄 Page Level</h3>
                    <div className="bg-white border border-blue-300 rounded p-4">
                        <div className="font-semibold text-blue-800">ReportWeighins</div>
                        <p className="text-sm text-blue-600">Página principal con layout completo</p>
                    </div>
                </div>

                <div className="bg-green-50 border-2 border-green-200 rounded-lg p-6">
                    <h3 className="text-xl font-bold text-green-900 mb-4">🔧 Organism Level</h3>
                    <div className="bg-white border border-green-300 rounded p-4">
                        <div className="font-semibold text-green-800">FiltersWeighins</div>
                        <p className="text-sm text-green-600">Panel completo de filtros en drawer</p>
                    </div>
                </div>

                <div className="bg-purple-50 border-2 border-purple-200 rounded-lg p-6">
                    <h3 className="text-xl font-bold text-purple-900 mb-4">⚛️ Molecule Level</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="bg-white border border-purple-300 rounded p-4">
                            <div className="font-semibold text-purple-800">LicensePlateFilter</div>
                            <p className="text-xs text-purple-600">Filtro de placas</p>
                        </div>
                        <div className="bg-white border border-purple-300 rounded p-4">
                            <div className="font-semibold text-purple-800">TicketNumberFilter</div>
                            <p className="text-xs text-purple-600">Filtro de tickets</p>
                        </div>
                        <div className="bg-white border border-purple-300 rounded p-4">
                            <div className="font-semibold text-purple-800">CompanyNameFilter</div>
                            <p className="text-xs text-purple-600">Filtro de empresas</p>
                        </div>
                    </div>
                </div>

                <div className="bg-orange-50 border-2 border-orange-200 rounded-lg p-6">
                    <h3 className="text-xl font-bold text-orange-900 mb-4">🪝 Hook Level</h3>
                    <div className="bg-white border border-orange-300 rounded p-4">
                        <div className="font-semibold text-orange-800">useReport</div>
                        <p className="text-sm text-orange-600">Lógica de negocio y gestión de estado</p>
                    </div>
                </div>
            </div>

            <div className="mt-8 p-6 bg-gray-50 rounded-lg">
                <h3 className="text-lg font-semibold mb-3">📊 Flujo de Datos</h3>
                <div className="text-sm space-y-2">
                    <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                        <span>ReportWeighins gestiona el estado global y layout</span>
                    </div>
                    <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span>FiltersWeighins coordina todos los filtros individuales</span>
                    </div>
                    <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                        <span>Cada filtro molecular maneja su propio estado local</span>
                    </div>
                    <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                        <span>useReport proporciona datos y lógica de negocio</span>
                    </div>
                </div>
            </div>
        </div>
    ),
    parameters: {
        docs: {
            description: {
                story: `
          Visualización de la jerarquía de componentes y el flujo de datos
          en el módulo Report Weighins, siguiendo principios de Atomic Design.
        `
            }
        }
    }
};
