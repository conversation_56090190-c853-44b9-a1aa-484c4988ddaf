export type ReportWeighins = {
  id: string;
  licensePlate: string;
  nit: number;
  arrivingWeight: number;
  leavingWeight: number;
  depositWeight: number;
  entryDate: string;
  egressDate: string;
  materialType: string;
  depositPlace: number;
  originType: string;
  nuap: number;
  loadingDate: string;
  cancelDate: string;
  loadingType: string;
  companyName: string;
  town: ReportWeighinsTown;
};

export type ReportWeighinsTown = {
  code: string;
  name: string;
  department: string;
  province: string;
};

export type WeighinsReportFiltersT = {
  fromDate: unknown;
  toDate: unknown;
  licensePlate: unknown;
  ticketNumber: unknown;
  nit: unknown;
  companyName: unknown;
};
