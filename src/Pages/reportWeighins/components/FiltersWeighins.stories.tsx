import type { <PERSON>a, StoryObj } from '@storybook/react';
import FiltersWeighins from './FiltersWeighins';
import { ReportWeighins } from '../models';

const mockTableData: ReportWeighins[] = [
  {
    id: 'WGH001',
    licensePlate: 'ABC123',
    nit: *********,
    arrivingWeight: 1500.5,
    leavingWeight: 800.0,
    depositWeight: 700.5,
    entryDate: '2024-01-15T08:30:00',
    egressDate: '2024-01-15T14:45:00',
    materialType: 'Mineral de oro',
    depositPlace: 1,
    originType: 'Mina',
    nuap: 123,
    loadingDate: '2024-01-15T09:00:00',
    cancelDate: '',
    loadingType: 'Carga completa',
    companyName: 'Minería El Dorado S.A.S.',
    town: {
      code: '05001',
      name: 'Medellín',
      department: 'Antioquia',
      province: 'Valle de Aburrá'
    }
  },
  {
    id: 'WGH002',
    licensePlate: 'XYZ789',
    nit: *********,
    arrivingWeight: 2000.0,
    leavingWeight: 1200.5,
    depositWeight: 799.5,
    entryDate: '2024-01-15T10:15:00',
    egressDate: '2024-01-15T16:30:00',
    materialType: 'Concentrado',
    depositPlace: 2,
    originType: 'Planta',
    nuap: 456,
    loadingDate: '2024-01-15T10:45:00',
    cancelDate: '',
    loadingType: 'Carga parcial',
    companyName: 'Procesadora Andina Ltda.',
    town: {
      code: '05002',
      name: 'Barbosa',
      department: 'Antioquia',
      province: 'Valle de Aburrá'
    }
  },
  {
    id: 'WGH003',
    licensePlate: 'DEF456',
    nit: *********,
    arrivingWeight: 1800.0,
    leavingWeight: 900.0,
    depositWeight: 900.0,
    entryDate: '2024-01-16T07:00:00',
    egressDate: '2024-01-16T13:15:00',
    materialType: 'Mineral aurífero',
    depositPlace: 1,
    originType: 'Mina',
    nuap: 789,
    loadingDate: '2024-01-16T07:30:00',
    cancelDate: '',
    loadingType: 'Carga completa',
    companyName: 'Exploración Minera Norte S.A.',
    town: {
      code: '05045',
      name: 'Bello',
      department: 'Antioquia',
      province: 'Valle de Aburrá'
    }
  }
];

const defaultProps = {
  anchor: 'right' as const,
  title: 'Filtros de Pesajes',
};

const meta: Meta<typeof FiltersWeighins> = {
  title: 'ReportWeighins/Organism/FiltersWeighins',
  component: FiltersWeighins,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: `
          **FiltersWeighins Component**
          
          A comprehensive filter organism that manages all filtering options for the Weighins report.
          
          **Features:**
          - Date range picker with start and end date selection
          - License plate filter with autocomplete
          - Ticket number filter with search functionality
          - Company name filter with NIT integration
          - Integration with Redux global filter state
          - Form validation and real-time updates
          - Clear and apply filter actions
          
          **Filter Types:**
          - **Date Range**: Select start and end dates for weighing records
          - **License Plate**: Filter by vehicle license plates from existing data
          - **Ticket Number**: Search by specific weighing ticket IDs
          - **Company**: Select by company name with NIT validation
          
          **Architecture:**
          - Uses Redux for state management
          - Connects to global filter slice
          - Handles form validation and formatting
          - Provides real-time filter updates
          - Integrates with backend APIs for company data
        `
      }
    }
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div className="max-w-md mx-auto bg-white border rounded-lg shadow-sm">
        <Story />
      </div>
    )
  ]
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: defaultProps,
  parameters: {
    docs: {
      description: {
        story: `
          Default state of the weighins filters with all filter options available.
          Shows the complete filter interface as it appears in the drawer.
        `
      }
    }
  }
};

export const WithTableData: Story = {
  args: defaultProps,
  parameters: {
    mockData: mockTableData,
    docs: {
      description: {
        story: `
          Filters component with mock table data to populate the license plate and ticket number dropdowns.
          Demonstrates how the filters adapt to available data.
        `
      }
    }
  }
};

export const InDrawerContext: Story = {
  args: defaultProps,
  decorators: [
    (Story) => (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
        <div className="bg-white h-full w-80 shadow-2xl">
          <Story />
        </div>
      </div>
    )
  ],
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        story: `
          Shows the filter component as it appears in the actual drawer overlay.
          Demonstrates the real usage context with proper backdrop and positioning.
        `
      }
    }
  }
};

export const CompactView: Story = {
  args: defaultProps,
  decorators: [
    (Story) => (
      <div className="max-w-xs mx-auto bg-white border rounded">
        <Story />
      </div>
    )
  ],
  parameters: {
    docs: {
      description: {
        story: `
          Compact version of the filters suitable for smaller containers or mobile views.
          Shows how the component adapts to constrained spaces.
        `
      }
    }
  }
};

export const FilterStates: Story = {
  args: defaultProps,
  decorators: [
    (Story) => (
      <div className="space-y-6">
        <div className="p-4 border rounded">
          <h3 className="text-lg font-semibold mb-3">Estado por defecto</h3>
          <div className="max-w-sm">
            <Story />
          </div>
        </div>
        <div className="p-4 border rounded bg-blue-50">
          <h3 className="text-lg font-semibold mb-3">Con filtros aplicados</h3>
          <div className="max-w-sm">
            <Story />
          </div>
        </div>
      </div>
    )
  ],
  parameters: {
    docs: {
      description: {
        story: `
          Comparison of different filter states showing default and applied filter scenarios.
          Useful for understanding the visual feedback provided to users.
        `
      }
    }
  }
};

export const InteractiveDemo: Story = {
  args: defaultProps,
  decorators: [
    (Story) => (
      <div className="max-w-2xl mx-auto p-6">
        <div className="mb-6">
          <h2 className="text-xl font-bold mb-2">Filtros de Pesajes - Demo Interactivo</h2>
          <p className="text-gray-600">
            Prueba los diferentes filtros y observa cómo se actualizan los valores.
          </p>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <Story />
          </div>
          <div className="space-y-4">
            <div className="p-4 bg-gray-50 rounded">
              <h3 className="font-semibold mb-2">Instrucciones:</h3>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• Selecciona un rango de fechas</li>
                <li>• Filtra por placa de vehículo</li>
                <li>• Busca por número de ticket</li>
                <li>• Selecciona una empresa</li>
                <li>• Aplica o limpia los filtros</li>
              </ul>
            </div>
            <div className="p-4 bg-blue-50 rounded">
              <h3 className="font-semibold mb-2">Funcionalidades:</h3>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• Autocompletado inteligente</li>
                <li>• Validación de fechas</li>
                <li>• Persistencia de filtros</li>
                <li>• Integración con APIs</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    )
  ],
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        story: `
          Interactive demo showcasing all filter functionalities with explanatory content.
          Perfect for understanding the complete user experience.
        `
      }
    }
  }
};
