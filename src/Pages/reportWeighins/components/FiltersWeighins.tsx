import { useFiltersStore } from '@/Orion.CORE.Frontend/store';
import { FilterWeighinsE, nameModule } from '@/Pages/reportWeighins/helpers';
import { AnchorT } from '@/Orion.CORE.Frontend/models';
import { useUrlReportWeighinsStore } from '@/Pages/reportWeighins/store/useReportWeighinsStore.ts';
import dayjs from 'dayjs';
import { WeighinsReportFiltersT } from '@/Pages/reportWeighins/models';
import buildUrlQueryParams from '@/helpers/buildUrlQueryParams.ts';
import { endpointsPathsBussiness } from '@/constants';
import { Box, Grid, List } from '@mui/material';
import { DrawerActionsFilters, DrawerTitle } from '@/Orion.CORE.Frontend/components';
import { DateRangeFilter } from '@/components';
import LicensePlateFilter from './filters/LicensePlateFilter';
import TicketNumberFilter from './filters/TicketNumberFilter';
import CompanyNameFilter from '@/Pages/reportWeighins/components/filters/CompanyNameFilter';

type PropsT = {
  anchor?: AnchorT;
  title: string;
};

export default function FiltersWeighins({ anchor = 'right', title }: Readonly<PropsT>) {
  const startDate = useFiltersStore((state) => state.data[FilterWeighinsE.startDate]);
  const endDate = useFiltersStore((state) => state.data[FilterWeighinsE.endDate]);
  const licencePlate = useFiltersStore((state) => state.data[FilterWeighinsE.licensePlate]);
  const ticketNumber = useFiltersStore((state) => state.data[FilterWeighinsE.ticketNumber]);
  const company = useFiltersStore((state) => state.data[FilterWeighinsE.company]);
  const reportUrlStore = useUrlReportWeighinsStore((state) => state);
  const tableData = useUrlReportWeighinsStore((state) => state.tableData);
  const setHandleOpenDrawer = useFiltersStore((state) => state.setHandleOpenDrawer);
  const setApplyFilters = useFiltersStore((state) => state.setApplyFilters);
  const setData = useFiltersStore((state) => state.setData);

  const handleClearFilters = () => {
    setData(FilterWeighinsE.startDate, { selectedDate: dayjs().startOf('month'), minDate: null, maxDate: dayjs() });
    setData(FilterWeighinsE.endDate, { selectedDate: dayjs().endOf('month'), minDate: dayjs().startOf('month'), maxDate: dayjs() });
    setData(FilterWeighinsE.NIT, { list: [], selectedRadioButton: undefined });
    setData(FilterWeighinsE.licensePlate, { list: [], selectedRadioButton: undefined });
    setData(FilterWeighinsE.ticketNumber, { list: [], selectedRadioButton: undefined });
    setData(FilterWeighinsE.company, { list: [], selectedRadioButton: undefined });
  };

  const handleApplyFilters = () => {
    setApplyFilters(nameModule, anchor);
    const selectedCompany = company?.selectedRadioButton;
    const companyNit = selectedCompany?.value;
    const companyName = selectedCompany?.label?.split(' - ')[1];

    const weighinsReportFilters: WeighinsReportFiltersT = {
      fromDate: dayjs(startDate.selectedDate)?.format('YYYY-MM-DD HH:MM:ss') || '',
      toDate: dayjs(endDate.selectedDate)?.format('YYYY-MM-DD HH:MM:ss') || '',
      licensePlate: licencePlate?.selectedRadioButton?.value,
      ticketNumber: ticketNumber?.selectedRadioButton?.value,
      nit: companyNit,
      companyName: companyName,
    };

    const url = buildUrlQueryParams(`${endpointsPathsBussiness.WEIGHINS}?`, weighinsReportFilters);

    const weighinsExportFilters = {
      fromDate: dayjs(startDate.selectedDate)?.format('YYYY-MM-DD HH:MM:ss') || '',
      toDate: dayjs(endDate.selectedDate)?.format('YYYY-MM-DD HH:MM:ss') || '',
      licensePlate: licencePlate?.selectedRadioButton?.value,
      weighinId: ticketNumber?.selectedRadioButton?.value,
      nit: companyNit,
      reportType: "2",
    };
    const urlExport = buildUrlQueryParams(`${endpointsPathsBussiness.REPORTS_EXPORT}?`, weighinsExportFilters);

    reportUrlStore.setUrlStore(url);
    reportUrlStore.setDownloadReportUrl(urlExport);
  };

  return (
    <Box
      className="filters-content"
      sx={{ width: anchor === 'top' || anchor === 'bottom' ? 'auto' : '320px', maxHeight: '100%' }}
      height="100%"
    >
      <Grid container height="100%" alignContent="space-between">
        <Grid>
          <DrawerTitle nameModule={nameModule} title={title} handleOpenDrawer={setHandleOpenDrawer} />
          <List disablePadding>
            <DateRangeFilter
              nameModule={nameModule}
              startDateFilterKey={FilterWeighinsE.startDate}
              startDateLabel={'Fecha de inicio'}
              endDateFilterKey={FilterWeighinsE.endDate}
              endDateLabel={'Fecha de fin'}
            />
            <LicensePlateFilter
              nameModule={nameModule}
              licensePlateFilterKey={FilterWeighinsE.licensePlate}
              tableData={tableData}
            />
            <TicketNumberFilter
              nameModule={nameModule}
              ticketNumberFilterKey={FilterWeighinsE.ticketNumber}
              tableData={tableData}
            />
            <CompanyNameFilter nameModule={nameModule} companyNameFilterKey={FilterWeighinsE.company} />
          </List>
        </Grid>
        <Grid container>
          <DrawerActionsFilters handleClearFilters={handleClearFilters} handleApplyFilters={handleApplyFilters} />
        </Grid>
      </Grid>
    </Box>
  );
}
