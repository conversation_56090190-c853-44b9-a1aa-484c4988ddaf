import { Check } from "@mui/icons-material";
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import { useEffect, useState } from "react";

import { getLocalStorage } from "@/Orion.CORE.Frontend/helpers";
import { FilterGenericsT } from "@/Orion.CORE.Frontend/models/StoreModels/FiltersStore.type";
import { useFiltersStore } from "@/Orion.CORE.Frontend/store";

import { Button } from "@/features/ui/button/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/features/ui/command/Command";
import { Popover, PopoverContent, PopoverTrigger } from "@/features/ui/popover/Popover";
import { cn } from "@/lib/utils";
import { ReportWeighins } from "@/Pages/reportWeighins/models";

type LicensePlateFilterProps = {
  nameModule: string;
  licensePlateFilterKey: string;
  tableData?: ReportWeighins[];
};

const LicensePlateFilter = ({ nameModule, licensePlateFilterKey, tableData = [] }: LicensePlateFilterProps) => {
  const [open, setOpen] = useState(false);
  const [filteredPlates, setFilteredPlates] = useState<{ value: string; label: string }[]>([]);

  const plateValues = useFiltersStore((state) => state.data[licensePlateFilterKey]);
  const setData = useFiltersStore((state) => state.setData);
  const handleSelected = useFiltersStore((state) => state.setSelectedRadioButton);

  const localStorageData: FilterGenericsT<string> = getLocalStorage(licensePlateFilterKey);

  const availablePlates = [...new Set(tableData.map(item => item.licensePlate))]
    .filter(Boolean)
    .map(plate => ({ value: plate, label: `${plate}` }));

  const initialstate: FilterGenericsT<string> = {
    list: availablePlates,
    selectedRadioButton: localStorageData?.selectedRadioButton,
  };

  useEffect(() => {
    setData(licensePlateFilterKey, initialstate);
    setFilteredPlates(availablePlates);
  }, [tableData, licensePlateFilterKey, setData]);

  const handleSearch = (value: string) => {
    const upperValue = value.toUpperCase();
    if (upperValue.trim()) {
      if (availablePlates.length > 0) {
        const filtered = availablePlates.filter(plate =>
          plate.value.toUpperCase().includes(upperValue)
        );
        setFilteredPlates(filtered);
      } else {
        const manualEntry = [{ value: upperValue.trim(), label: `${upperValue.trim()}` }];
        setFilteredPlates(manualEntry);
      }
    } else {
      setFilteredPlates(availablePlates);
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <div className="px-4">
          <Button
            aria-expanded={open}
            className="w-full my-2 p-0 gap-4 justify-start"
            variant="ghost"
          >
            {open ? (
              <ArrowBackIosNewIcon
                sx={(theme) => ({ fontSize: "11px", color: theme.palette.primary.main })}
              />
            ) : (
              <ArrowForwardIosIcon
                sx={(theme) => ({ fontSize: "12px", color: theme.palette.grey[900] })}
              />
            )}
            <div>
              <p className="text-start w-full font-semibold text-[12px]">Placa</p>
              <p className="w-full text-[#9e9e9e] text-start font-normal truncate max-w-[200px]">
                {plateValues?.selectedRadioButton?.label || "Seleccionar placa"}
              </p>
            </div>
          </Button>
        </div>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-[300px] h-[200px] p-0" side="left" sideOffset={5}>
        <Command shouldFilter={false}>
          <CommandInput
            placeholder="Buscar placa..."
            onValueChange={handleSearch}
          />
          {filteredPlates.length === 0 && availablePlates.length === 0 && (
            <CommandEmpty>Escriba para buscar placa...</CommandEmpty>
          )}
          {filteredPlates.length === 0 && availablePlates.length > 0 && (
            <CommandEmpty>No se encontraron placas</CommandEmpty>
          )}
          <CommandGroup className="max-h-[140px] overflow-y-auto">
            <CommandList className="max-h-none">
              {filteredPlates.map((plate) => (
                <CommandItem
                  key={plate.value}
                  value={plate.value}
                  className="flex items-start py-2 px-3"
                  onSelect={(currentValue) => {
                    handleSelected(nameModule, licensePlateFilterKey, {
                      value: currentValue.toUpperCase(),
                      label: plate.label,
                    });
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4 mt-0.5 flex-shrink-0",
                      plateValues?.selectedRadioButton &&
                        plateValues?.selectedRadioButton?.value === plate.value
                        ? "opacity-100"
                        : "opacity-0",
                    )}
                  />
                  <span className="flex-1 break-words">{plate.label}</span>
                </CommandItem>
              ))}
            </CommandList>
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default LicensePlateFilter;
