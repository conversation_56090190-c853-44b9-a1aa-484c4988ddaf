import type { <PERSON>a, StoryObj } from '@storybook/react';
import TicketNumberFilter from './TicketNumberFilter';
import { ReportWeighins } from '../../models';

const mockTableData: ReportWeighins[] = [
  {
    id: 'WGH001',
    licensePlate: 'ABC123',
    nit: *********,
    arrivingWeight: 1500.5,
    leavingWeight: 800.0,
    depositWeight: 700.5,
    entryDate: '2024-01-15T08:30:00',
    egressDate: '2024-01-15T14:45:00',
    materialType: 'Mineral de oro',
    depositPlace: 1,
    originType: 'Mina',
    nuap: 123,
    loadingDate: '2024-01-15T09:00:00',
    cancelDate: '',
    loadingType: 'Carga completa',
    companyName: 'Minería El Dorado S.A.S.',
    town: {
      code: '05001',
      name: 'Medellín',
      department: 'Antioquia',
      province: 'Valle de Aburrá'
    }
  },
  {
    id: 'WGH002',
    licensePlate: 'XYZ789',
    nit: *********,
    arrivingWeight: 2000.0,
    leavingWeight: 1200.5,
    depositWeight: 799.5,
    entryDate: '2024-01-15T10:15:00',
    egressDate: '2024-01-15T16:30:00',
    materialType: 'Concentrado',
    depositPlace: 2,
    originType: 'Planta',
    nuap: 456,
    loadingDate: '2024-01-15T10:45:00',
    cancelDate: '',
    loadingType: 'Carga parcial',
    companyName: 'Procesadora Andina Ltda.',
    town: {
      code: '05002',
      name: 'Barbosa',
      department: 'Antioquia',
      province: 'Valle de Aburrá'
    }
  },
  {
    id: 'WGH003',
    licensePlate: 'DEF456',
    nit: *********,
    arrivingWeight: 1800.0,
    leavingWeight: 900.0,
    depositWeight: 900.0,
    entryDate: '2024-01-16T07:00:00',
    egressDate: '2024-01-16T13:15:00',
    materialType: 'Mineral aurífero',
    depositPlace: 1,
    originType: 'Mina',
    nuap: 789,
    loadingDate: '2024-01-16T07:30:00',
    cancelDate: '',
    loadingType: 'Carga completa',
    companyName: 'Exploración Minera Norte S.A.',
    town: {
      code: '05045',
      name: 'Bello',
      department: 'Antioquia',
      province: 'Valle de Aburrá'
    }
  },
  {
    id: 'WGH004',
    licensePlate: 'GHI789',
    nit: *********,
    arrivingWeight: 1200.0,
    leavingWeight: 600.0,
    depositWeight: 600.0,
    entryDate: '2024-01-16T09:00:00',
    egressDate: '2024-01-16T15:30:00',
    materialType: 'Concentrado',
    depositPlace: 3,
    originType: 'Planta',
    nuap: 321,
    loadingDate: '2024-01-16T09:30:00',
    cancelDate: '',
    loadingType: 'Carga parcial',
    companyName: 'Minerales del Sur Ltda.',
    town: {
      code: '05088',
      name: 'Bello',
      department: 'Antioquia',
      province: 'Valle de Aburrá'
    }
  },
  {
    id: 'WGH005',
    licensePlate: 'JKL012',
    nit: 12345678,
    arrivingWeight: 2200.0,
    leavingWeight: 1100.0,
    depositWeight: 1100.0,
    entryDate: '2024-01-17T08:15:00',
    egressDate: '2024-01-17T14:45:00',
    materialType: 'Mineral de oro',
    depositPlace: 2,
    originType: 'Mina',
    nuap: 654,
    loadingDate: '2024-01-17T08:45:00',
    cancelDate: '',
    loadingType: 'Carga completa',
    companyName: 'Aurífera Colombiana S.A.',
    town: {
      code: '05129',
      name: 'Caldas',
      department: 'Antioquia',
      province: 'Valle de Aburrá'
    }
  }
];

const defaultProps = {
  nameModule: 'weighins',
  ticketNumberFilterKey: 'ticketNumber',
  tableData: mockTableData,
};

const meta: Meta<typeof TicketNumberFilter> = {
  title: 'ReportWeighins/Molecule/TicketNumberFilter',
  component: TicketNumberFilter,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: `
          **TicketNumberFilter Component**
          
          A specialized filter component for selecting specific weighing ticket numbers from records.
          
          **Features:**
          - Autocomplete search for ticket numbers
          - Dynamic data population from weighing records
          - Integration with global filter state
          - Real-time search with instant filtering
          - Keyboard navigation and accessibility
          - Clear selection functionality
          
          **Data Source:**
          - Extracts unique ticket IDs from table data
          - Filters out empty or null values
          - Provides formatted dropdown options with ticket numbers
          
          **Use Cases:**
          - Finding specific weighing transactions
          - Filtering by unique ticket identifiers
          - Quick lookup of weighing records
          - Audit trail and record verification
          
          **User Experience:**
          - Type-ahead search functionality
          - Visual feedback for selected tickets
          - Clear and intuitive interface
          - Responsive design for all devices
          
          **Integration:**
          - Connects to Redux filter store
          - Persists selections in localStorage
          - Triggers filter updates on selection change
        `
      }
    }
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div className="max-w-sm mx-auto p-4 bg-white border rounded-lg">
        <Story />
      </div>
    )
  ]
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: defaultProps,
  parameters: {
    docs: {
      description: {
        story: `
          Default state of the ticket number filter with sample weighing data.
          Shows all available ticket numbers from the mock records.
        `
      }
    }
  }
};

export const EmptyData: Story = {
  args: {
    ...defaultProps,
    tableData: [],
  },
  parameters: {
    docs: {
      description: {
        story: `
          Filter component when no weighing data is available.
          Demonstrates graceful handling of empty datasets.
        `
      }
    }
  }
};

export const WithSelection: Story = {
  args: defaultProps,
  parameters: {
    docs: {
      description: {
        story: `
          Example showing the filter with a pre-selected ticket number.
          Displays the selected state and visual feedback to users.
        `
      }
    }
  }
};

export const SearchInteraction: Story = {
  args: defaultProps,
  decorators: [
    (Story) => (
      <div className="max-w-md mx-auto p-6 space-y-4">
        <div>
          <h3 className="text-lg font-semibold mb-2">Filtro de Número de Ticket</h3>
          <p className="text-gray-600 text-sm mb-4">
            Busca y selecciona un número de ticket específico de los registros de pesaje.
          </p>
          <Story />
        </div>
        <div className="p-4 bg-gray-50 rounded">
          <h4 className="font-medium mb-2">Tickets disponibles:</h4>
          <div className="grid grid-cols-3 gap-2">
            {mockTableData.map((item) => (
              <span key={item.id} className="px-3 py-1 bg-green-100 text-green-800 rounded text-sm font-mono">
                {item.id}
              </span>
            ))}
          </div>
        </div>
        <div className="p-4 bg-blue-50 rounded">
          <h4 className="font-medium mb-2">Características:</h4>
          <ul className="text-sm space-y-1 text-gray-600">
            <li>• Búsqueda instantánea mientras escribes</li>
            <li>• Identificadores únicos de pesajes</li>
            <li>• Validación automática de datos</li>
            <li>• Integración con filtros globales</li>
          </ul>
        </div>
      </div>
    )
  ],
  parameters: {
    docs: {
      description: {
        story: `
          Interactive demo showing the search functionality with ticket numbers and explanatory content.
          Perfect for understanding the component's purpose and available data.
        `
      }
    }
  }
};

export const LargeDataset: Story = {
  args: {
    ...defaultProps,
    tableData: [
      ...mockTableData,
      ...Array.from({ length: 50 }, (_, i) => ({
        ...mockTableData[0],
        id: `WGH${String(i + 6).padStart(3, '0')}`,
        licensePlate: `VEH${String(i + 1).padStart(3, '0')}`,
      })),
    ],
  },
  decorators: [
    (Story) => (
      <div className="max-w-md mx-auto p-6">
        <div className="mb-4">
          <h3 className="text-lg font-semibold mb-2">Conjunto de datos grande</h3>
          <p className="text-gray-600 text-sm">
            Con más de 50 tickets disponibles, demuestra la eficiencia del componente.
          </p>
        </div>
        <Story />
        <div className="mt-4 p-3 bg-yellow-50 rounded">
          <p className="text-sm text-yellow-800">
            💡 Escribe "WGH" para filtrar rápidamente los tickets de pesaje.
          </p>
        </div>
      </div>
    )
  ],
  parameters: {
    docs: {
      description: {
        story: `
          Example with a large dataset to demonstrate search performance and scalability.
          Shows how the component efficiently handles many ticket options.
        `
      }
    }
  }
};

export const ValidationStates: Story = {
  args: defaultProps,
  decorators: [
    (Story) => (
      <div className="space-y-6">
        <div className="p-4 border rounded">
          <h4 className="font-medium mb-2 text-gray-700">Estado normal</h4>
          <Story />
        </div>
        <div className="p-4 border border-green-200 bg-green-50 rounded">
          <h4 className="font-medium mb-2 text-green-700">Con ticket seleccionado</h4>
          <Story />
        </div>
        <div className="p-4 border border-blue-200 bg-blue-50 rounded">
          <h4 className="font-medium mb-2 text-blue-700">Buscando...</h4>
          <Story />
        </div>
      </div>
    )
  ],
  parameters: {
    docs: {
      description: {
        story: `
          Different visual states of the ticket number filter component.
          Shows normal, selected, and search states for better understanding.
        `
      }
    }
  }
};

export const MobileResponsive: Story = {
  args: defaultProps,
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    },
    docs: {
      description: {
        story: `
          Mobile-optimized view of the ticket number filter.
          Demonstrates responsive behavior and touch-friendly interactions.
        `
      }
    }
  }
};
