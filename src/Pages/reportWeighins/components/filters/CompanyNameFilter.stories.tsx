import type { Meta, StoryObj } from '@storybook/react';
import CompanyNameFilter from './CompanyNameFilter';

const mockCompanyData = [
  { value: '*********', name: 'Minería El Dorado S.A.S.' },
  { value: '*********', name: 'Procesadora Andina Ltda.' },
  { value: '*********', name: 'Exploración Minera Norte S.A.' },
  { value: '*********', name: 'Minerales del Sur Ltda.' },
  { value: '12345678', name: 'Aurífera Colombiana S.A.' },
  { value: '*********', name: 'Extractora Valle Verde Ltda.' },
  { value: '*********', name: 'Compañía Minera Antioquia S.A.' },
  { value: '*********', name: 'Procesamiento Minero Integral Ltda.' },
  { value: '*********', name: 'Tecnología Minera Avanzada S.A.S.' },
  { value: '*********', name: 'Recursos Naturales del Pacífico Ltda.' }
];

const defaultProps = {
  nameModule: 'weighins',
  companyNameFilterKey: 'company',
};

const meta: Meta<typeof CompanyNameFilter> = {
  title: 'ReportWeighins/Molecule/CompanyNameFilter',
  component: CompanyNameFilter,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: `
          **CompanyNameFilter Component**
          
          A specialized filter component for selecting companies by name with NIT (tax ID) integration.
          
          **Features:**
          - Company search with autocomplete functionality
          - NIT (tax identification number) integration
          - Dynamic data loading from external API
          - Real-time search and filtering
          - Integration with global filter state
          - Formatted display with NIT - Company Name format
          
          **Data Source:**
          - Fetches company data from NIT service API
          - Provides formatted dropdown options
          - Includes both company name and tax ID
          - Filters companies in real-time
          
          **Use Cases:**
          - Filter weighing records by specific companies
          - Search companies by name or NIT
          - Legal entity verification and tracking
          - Compliance and audit reporting
          
          **User Experience:**
          - Type-ahead search functionality
          - Clear visual separation of NIT and company name
          - Keyboard navigation support
          - Responsive design for all devices
          
          **Integration:**
          - Connects to Redux filter store
          - Persists selections in localStorage
          - Integrates with external NIT validation service
          - Triggers filter updates on company selection
        `
      }
    }
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div className="max-w-sm mx-auto p-4 bg-white border rounded-lg">
        <Story />
      </div>
    )
  ]
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: defaultProps,
  parameters: {
    mockData: [
      {
        url: '**/nit**',
        method: 'GET',
        status: 200,
        response: mockCompanyData
      }
    ],
    docs: {
      description: {
        story: `
          Default state of the company name filter with sample company data.
          Shows companies formatted with NIT - Company Name structure.
        `
      }
    }
  }
};

export const LoadingState: Story = {
  args: defaultProps,
  parameters: {
    mockData: [
      {
        url: '**/nit**',
        method: 'GET',
        delay: 2000,
        status: 200,
        response: mockCompanyData
      }
    ],
    docs: {
      description: {
        story: `
          Loading state while company data is being fetched from the API.
          Shows appropriate loading feedback to users.
        `
      }
    }
  }
};

export const EmptyData: Story = {
  args: defaultProps,
  parameters: {
    mockData: [
      {
        url: '**/nit**',
        method: 'GET',
        status: 200,
        response: []
      }
    ],
    docs: {
      description: {
        story: `
          Filter component when no company data is available from the API.
          Demonstrates graceful handling of empty datasets.
        `
      }
    }
  }
};

export const ErrorState: Story = {
  args: defaultProps,
  parameters: {
    mockData: [
      {
        url: '**/nit**',
        method: 'GET',
        status: 500,
        response: { error: 'Internal server error' }
      }
    ],
    docs: {
      description: {
        story: `
          Error state when the company data API fails.
          Shows how the component handles API errors gracefully.
        `
      }
    }
  }
};

export const SearchInteraction: Story = {
  args: defaultProps,
  decorators: [
    (Story) => (
      <div className="max-w-lg mx-auto p-6 space-y-4">
        <div>
          <h3 className="text-lg font-semibold mb-2">Filtro de Empresa</h3>
          <p className="text-gray-600 text-sm mb-4">
            Busca y selecciona una empresa usando su nombre o NIT.
          </p>
          <Story />
        </div>
        <div className="p-4 bg-gray-50 rounded">
          <h4 className="font-medium mb-2">Empresas disponibles:</h4>
          <div className="space-y-1 max-h-40 overflow-y-auto">
            {mockCompanyData.map((company) => (
              <div key={company.value} className="flex justify-between text-sm py-1 px-2 bg-white rounded">
                <span className="font-mono text-blue-600">{company.value}</span>
                <span className="text-gray-700">{company.name}</span>
              </div>
            ))}
          </div>
        </div>
        <div className="p-4 bg-blue-50 rounded">
          <h4 className="font-medium mb-2">Funcionalidades:</h4>
          <ul className="text-sm space-y-1 text-gray-600">
            <li>• Búsqueda por nombre de empresa o NIT</li>
            <li>• Integración con base de datos fiscal</li>
            <li>• Validación automática de datos</li>
            <li>• Formato estándar NIT - Nombre</li>
          </ul>
        </div>
      </div>
    )
  ],
  parameters: {
    mockData: [
      {
        url: '**/nit**',
        method: 'GET',
        status: 200,
        response: mockCompanyData
      }
    ],
    docs: {
      description: {
        story: `
          Interactive demo showing the company search functionality with available options.
          Perfect for understanding the component's integration with NIT services.
        `
      }
    }
  }
};

export const LargeCompanyList: Story = {
  args: defaultProps,
  parameters: {
    mockData: [
      {
        url: '**/nit**',
        method: 'GET',
        status: 200,
        response: [
          ...mockCompanyData,
          ...Array.from({ length: 40 }, (_, i) => ({
            value: `9${String(i + 1000000).padStart(8, '0')}`,
            name: `Empresa Minera ${String.fromCharCode(65 + (i % 26))}${Math.floor(i / 26) + 1} S.A.S.`
          }))
        ]
      }
    ],
    docs: {
      description: {
        story: `
          Example with a large company dataset to demonstrate search performance.
          Shows how the component efficiently handles many company options.
        `
      }
    }
  }
};

export const SelectedState: Story = {
  args: defaultProps,
  decorators: [
    (Story) => (
      <div className="max-w-md mx-auto p-6">
        <div className="mb-4">
          <h3 className="text-lg font-semibold mb-2">Estado con empresa seleccionada</h3>
          <p className="text-gray-600 text-sm">
            Muestra cómo se ve el filtro cuando una empresa ha sido seleccionada.
          </p>
        </div>
        <Story />
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded">
          <p className="text-sm text-green-800">
            ✓ Empresa seleccionada: Se aplicará al filtro de pesajes
          </p>
        </div>
      </div>
    )
  ],
  parameters: {
    mockData: [
      {
        url: '**/nit**',
        method: 'GET',
        status: 200,
        response: mockCompanyData
      }
    ],
    docs: {
      description: {
        story: `
          Shows the filter component with a company pre-selected.
          Demonstrates the selected state and visual feedback.
        `
      }
    }
  }
};

export const ValidationAndFormat: Story = {
  args: defaultProps,
  decorators: [
    (Story) => (
      <div className="space-y-6">
        <div className="p-4 border rounded">
          <h4 className="font-medium mb-2 text-gray-700">Formato estándar</h4>
          <p className="text-sm text-gray-600 mb-3">NIT - Nombre de la empresa</p>
          <Story />
        </div>
        <div className="p-4 bg-amber-50 border border-amber-200 rounded">
          <h4 className="font-medium mb-2 text-amber-800">Validación NIT</h4>
          <ul className="text-sm space-y-1 text-amber-700">
            <li>• Números de identificación tributaria válidos</li>
            <li>• Verificación contra base de datos fiscal</li>
            <li>• Formato consistente para reportes</li>
          </ul>
        </div>
      </div>
    )
  ],
  parameters: {
    mockData: [
      {
        url: '**/nit**',
        method: 'GET',
        status: 200,
        response: mockCompanyData
      }
    ],
    docs: {
      description: {
        story: `
          Demonstrates the NIT validation and formatting features.
          Shows how the component ensures data consistency and compliance.
        `
      }
    }
  }
};

export const MobileResponsive: Story = {
  args: defaultProps,
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    },
    mockData: [
      {
        url: '**/nit**',
        method: 'GET',
        status: 200,
        response: mockCompanyData
      }
    ],
    docs: {
      description: {
        story: `
          Mobile-optimized view of the company name filter.
          Demonstrates responsive behavior and touch-friendly interactions.
        `
      }
    }
  }
};
