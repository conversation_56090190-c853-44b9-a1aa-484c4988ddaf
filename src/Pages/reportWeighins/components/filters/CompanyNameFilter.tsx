import { Check } from "@mui/icons-material";
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import { useEffect, useState } from "react";

import { getLocalStorage } from "@/Orion.CORE.Frontend/helpers";
import { FilterGenericsT } from "@/Orion.CORE.Frontend/models/StoreModels/FiltersStore.type";
import { useFiltersStore } from "@/Orion.CORE.Frontend/store";

import { Button } from "@/features/ui/button/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/features/ui/command/Command";
import { Popover, PopoverContent, PopoverTrigger } from "@/features/ui/popover/Popover";
import { cn } from "@/lib/utils";
import { useGetNit } from "@/service/api/getNit";

import { useFilterNits } from "@/Pages/reportFormat34/hook/useFilterNits";

const CompanyNameFilter = ({ nameModule, companyNameFilterKey }: { nameModule: string; companyNameFilterKey: string }) => {
  const [open, setOpen] = useState(false);
  const companyValues = useFiltersStore((state) => state.data[companyNameFilterKey]);
  const setData = useFiltersStore((state) => state.setData);
  const handleSelected = useFiltersStore((state) => state.setSelectedRadioButton);

  const { data: nitService, isSuccess } = useGetNit();

  const { filteredNit, onChange } = useFilterNits(nitService);

  const localStorageData: FilterGenericsT<string> = getLocalStorage(companyNameFilterKey);

  const companyFoundInLocalStorage = () => {
    if (!localStorageData) return;
    const companyFound = nitService?.find(
      (nit) =>
        nit.name === localStorageData.selectedRadioButton?.label &&
        nit.value === localStorageData.selectedRadioButton.value,
    );
    if (!companyFound) return;

    return { value: companyFound.value, label: `${companyFound.value} - ${companyFound.name}` };
  };

  const initialstate: FilterGenericsT<string> = {
    list: nitService?.map((nit) => ({ value: nit.value, label: `${nit.value} - ${nit.name}` })) ?? [],
    selectedRadioButton: companyFoundInLocalStorage()
      ? companyFoundInLocalStorage()
      : localStorageData?.selectedRadioButton,
  };

  useEffect(() => {
    setData(companyNameFilterKey, initialstate);
  }, [nitService]);

  if (isSuccess)
    return (
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div className="px-4">
            <Button
              aria-expanded={open}
              className="w-full my-2  p-0 gap-4 justify-start "
              variant="ghost"
            >
              {open ? (
                <ArrowBackIosNewIcon
                  sx={(theme) => ({ fontSize: "11px", color: theme.palette.primary.main })}
                />
              ) : (
                <ArrowForwardIosIcon
                  sx={(theme) => ({ fontSize: "12px", color: theme.palette.grey[900] })}
                />
              )}
              <div>
                <p className="text-start w-full font-semibold text-[12px]">Empresa</p>
                <p className="w-full text-[#9e9e9e] text-start font-normal truncate max-w-[200px]">
                  {companyValues?.selectedRadioButton?.label || "Seleccionar empresa"}
                </p>
              </div>
            </Button>
          </div>
        </PopoverTrigger>
        <PopoverContent align="start" className="w-[300px] h-[200px] p-0" side="left" sideOffset={5}>
          <Command shouldFilter={false} onChange={onChange}>
            <CommandInput placeholder="Filtrar por NIT o empresa..." />
            {filteredNit?.length < 1 && <CommandEmpty>No se encontraron empresas</CommandEmpty>}
            <CommandGroup className="max-h-[140px] overflow-y-auto">
              <CommandList className="max-h-none">
                {filteredNit?.map((nit) => (
                  <CommandItem
                    key={nit.value}
                    value={nit.value}
                    className="flex items-start py-2 px-3"
                    onSelect={(currentValue) => {
                      const selectedNit = nitService?.find((n) => n.value === currentValue);
                      handleSelected(nameModule, companyNameFilterKey, {
                        value: currentValue,
                        label: selectedNit ? `${selectedNit.value} - ${selectedNit.name}` : currentValue,
                      });
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4 mt-0.5 flex-shrink-0",
                        companyValues?.selectedRadioButton &&
                          companyValues?.selectedRadioButton?.value === nit.value
                          ? "opacity-100"
                          : "opacity-0",
                      )}
                    />
                    <span className="flex-1 break-words">{nit.value} - {nit.name}</span>
                  </CommandItem>
                ))}
              </CommandList>
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>
    );

  return null;
};

export default CompanyNameFilter;
