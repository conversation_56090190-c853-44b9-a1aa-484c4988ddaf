import { Check } from "@mui/icons-material";
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import { useEffect, useState } from "react";

import { getLocalStorage } from "@/Orion.CORE.Frontend/helpers";
import { FilterGenericsT } from "@/Orion.CORE.Frontend/models/StoreModels/FiltersStore.type";
import { useFiltersStore } from "@/Orion.CORE.Frontend/store";

import { Button } from "@/features/ui/button/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/features/ui/command/Command";
import { Popover, PopoverContent, PopoverTrigger } from "@/features/ui/popover/Popover";
import { cn } from "@/lib/utils";
import { ReportWeighins } from "@/Pages/reportWeighins/models";

type TicketNumberFilterProps = {
  nameModule: string;
  ticketNumberFilterKey: string;
  tableData?: ReportWeighins[];
};

const TicketNumberFilter = ({ nameModule, ticketNumberFilterKey, tableData = [] }: TicketNumberFilterProps) => {
  const [open, setOpen] = useState(false);
  const [filteredTickets, setFilteredTickets] = useState<{ value: string; label: string }[]>([]);

  const ticketValues = useFiltersStore((state) => state.data[ticketNumberFilterKey]);
  const setData = useFiltersStore((state) => state.setData);
  const handleSelected = useFiltersStore((state) => state.setSelectedRadioButton);

  const localStorageData: FilterGenericsT<string> = getLocalStorage(ticketNumberFilterKey);

  const availableTickets = [...new Set(tableData.map(item => item.id))]
    .filter(Boolean)
    .map(id => ({ value: id, label: `${id}` }));

  const initialstate: FilterGenericsT<string> = {
    list: availableTickets,
    selectedRadioButton: localStorageData?.selectedRadioButton,
  };

  useEffect(() => {
    setData(ticketNumberFilterKey, initialstate);
    setFilteredTickets(availableTickets);
  }, [tableData, ticketNumberFilterKey, setData]);

  const handleSearch = (value: string) => {
    if (value.trim()) {
      if (availableTickets.length > 0) {
        const filtered = availableTickets.filter(ticket =>
          ticket.value.toLowerCase().includes(value.toLowerCase())
        );
        setFilteredTickets(filtered);
      } else {
        const manualEntry = [{ value: value.trim(), label: `${value.trim()}` }];
        setFilteredTickets(manualEntry);
      }
    } else {
      setFilteredTickets(availableTickets);
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <div className="px-4">
          <Button
            aria-expanded={open}
            className="w-full my-2 p-0 gap-4 justify-start"
            variant="ghost"
          >
            {open ? (
              <ArrowBackIosNewIcon
                sx={(theme) => ({ fontSize: "11px", color: theme.palette.primary.main })}
              />
            ) : (
              <ArrowForwardIosIcon
                sx={(theme) => ({ fontSize: "12px", color: theme.palette.grey[900] })}
              />
            )}
            <div>
              <p className="text-start w-full font-semibold text-[12px]">Nro. Ticket</p>
              <p className="w-full text-[#9e9e9e] text-start font-normal truncate max-w-[200px]">
                {ticketValues?.selectedRadioButton?.label || "Seleccionar número de ticket"}
              </p>
            </div>
          </Button>
        </div>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-[300px] h-[200px] p-0" side="left" sideOffset={5}>
        <Command shouldFilter={false}>
          <CommandInput
            placeholder="Buscar número de ticket..."
            onValueChange={handleSearch}
          />
          {filteredTickets.length === 0 && (
            <CommandEmpty>
              {availableTickets.length === 0
                ? "Escriba para buscar número de ticket..."
                : "No se encontraron tickets"
              }
            </CommandEmpty>
          )}
          <CommandGroup className="max-h-[140px] overflow-y-auto">
            <CommandList className="max-h-none">
              {filteredTickets.map((ticket) => (
                <CommandItem
                  key={ticket.value}
                  value={ticket.value}
                  className="flex items-start py-2 px-3"
                  onSelect={(currentValue) => {
                    handleSelected(nameModule, ticketNumberFilterKey, {
                      value: currentValue,
                      label: ticket.label,
                    });
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4 mt-0.5 flex-shrink-0",
                      ticketValues?.selectedRadioButton &&
                        ticketValues?.selectedRadioButton?.value === ticket.value
                        ? "opacity-100"
                        : "opacity-0",
                    )}
                  />
                  <span className="flex-1 break-words">{ticket.label}</span>
                </CommandItem>
              ))}
            </CommandList>
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default TicketNumberFilter;
