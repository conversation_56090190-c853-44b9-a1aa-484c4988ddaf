import type { <PERSON>a, StoryObj } from '@storybook/react';
import LicensePlateFilter from './LicensePlateFilter';
import { ReportWeighins } from '../../models';

const mockTableData: ReportWeighins[] = [
    {
        id: 'WGH001',
        licensePlate: 'ABC123',
        nit: *********,
        arrivingWeight: 1500.5,
        leavingWeight: 800.0,
        depositWeight: 700.5,
        entryDate: '2024-01-15T08:30:00',
        egressDate: '2024-01-15T14:45:00',
        materialType: 'Mineral de oro',
        depositPlace: 1,
        originType: 'Mina',
        nuap: 123,
        loadingDate: '2024-01-15T09:00:00',
        cancelDate: '',
        loadingType: 'Carga completa',
        companyName: 'Minería El Dorado S.A.S.',
        town: {
            code: '05001',
            name: 'Medellín',
            department: 'Antioquia',
            province: 'Valle de Aburrá'
        }
    },
    {
        id: 'WGH002',
        licensePlate: 'XYZ789',
        nit: *********,
        arrivingWeight: 2000.0,
        leavingWeight: 1200.5,
        depositWeight: 799.5,
        entryDate: '2024-01-15T10:15:00',
        egressDate: '2024-01-15T16:30:00',
        materialType: 'Concentrado',
        depositPlace: 2,
        originType: 'Planta',
        nuap: 456,
        loadingDate: '2024-01-15T10:45:00',
        cancelDate: '',
        loadingType: 'Carga parcial',
        companyName: 'Procesadora Andina Ltda.',
        town: {
            code: '05002',
            name: 'Barbosa',
            department: 'Antioquia',
            province: 'Valle de Aburrá'
        }
    },
    {
        id: 'WGH003',
        licensePlate: 'DEF456',
        nit: *********,
        arrivingWeight: 1800.0,
        leavingWeight: 900.0,
        depositWeight: 900.0,
        entryDate: '2024-01-16T07:00:00',
        egressDate: '2024-01-16T13:15:00',
        materialType: 'Mineral aurífero',
        depositPlace: 1,
        originType: 'Mina',
        nuap: 789,
        loadingDate: '2024-01-16T07:30:00',
        cancelDate: '',
        loadingType: 'Carga completa',
        companyName: 'Exploración Minera Norte S.A.',
        town: {
            code: '05045',
            name: 'Bello',
            department: 'Antioquia',
            province: 'Valle de Aburrá'
        }
    },
    {
        id: 'WGH004',
        licensePlate: 'GHI789',
        nit: *********,
        arrivingWeight: 1200.0,
        leavingWeight: 600.0,
        depositWeight: 600.0,
        entryDate: '2024-01-16T09:00:00',
        egressDate: '2024-01-16T15:30:00',
        materialType: 'Concentrado',
        depositPlace: 3,
        originType: 'Planta',
        nuap: 321,
        loadingDate: '2024-01-16T09:30:00',
        cancelDate: '',
        loadingType: 'Carga parcial',
        companyName: 'Minerales del Sur Ltda.',
        town: {
            code: '05088',
            name: 'Bello',
            department: 'Antioquia',
            province: 'Valle de Aburrá'
        }
    }
];

const defaultProps = {
    nameModule: 'weighins',
    licensePlateFilterKey: 'licensePlate',
    tableData: mockTableData,
};

const meta: Meta<typeof LicensePlateFilter> = {
    title: 'ReportWeighins/Molecule/LicensePlateFilter',
    component: LicensePlateFilter,
    parameters: {
        layout: 'padded',
        docs: {
            description: {
                component: `
          **LicensePlateFilter Component**
          
          A specialized filter component for selecting vehicle license plates from weighing records.
          
          **Features:**
          - Autocomplete search functionality
          - Dynamic data population from table records
          - Integration with global filter state
          - Real-time search with filtering
          - Keyboard navigation support
          - Clear selection functionality
          
          **Data Source:**
          - Extracts unique license plates from table data
          - Filters out empty or null values
          - Provides formatted dropdown options
          
          **User Experience:**
          - Type-ahead search functionality
          - Visual feedback for selected values
          - Clear and intuitive interface
          - Responsive design for different screen sizes
          
          **Integration:**
          - Connects to Redux filter store
          - Persists selections in localStorage
          - Triggers filter updates on selection
        `
            }
        }
    },
    tags: ['autodocs'],
    decorators: [
        (Story) => (
            <div className="max-w-sm mx-auto p-4 bg-white border rounded-lg">
                <Story />
            </div>
        )
    ]
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    args: defaultProps,
    parameters: {
        docs: {
            description: {
                story: `
          Default state of the license plate filter with sample data.
          Shows all available license plates from the mock table data.
        `
            }
        }
    }
};

export const EmptyData: Story = {
    args: {
        ...defaultProps,
        tableData: [],
    },
    parameters: {
        docs: {
            description: {
                story: `
          Filter component when no table data is available.
          Shows how the component handles empty datasets gracefully.
        `
            }
        }
    }
};

export const WithSelection: Story = {
    args: defaultProps,
    parameters: {
        docs: {
            description: {
                story: `
          Example showing the filter with a pre-selected license plate.
          Demonstrates the selected state and visual feedback.
        `
            }
        }
    }
};

export const SearchDemo: Story = {
    args: defaultProps,
    decorators: [
        (Story) => (
            <div className="max-w-md mx-auto p-6 space-y-4">
                <div>
                    <h3 className="text-lg font-semibold mb-2">Filtro de Placa de Vehículo</h3>
                    <p className="text-gray-600 text-sm mb-4">
                        Busca y selecciona una placa de vehículo de los registros disponibles.
                    </p>
                    <Story />
                </div>
                <div className="p-4 bg-gray-50 rounded">
                    <h4 className="font-medium mb-2">Placas disponibles:</h4>
                    <div className="flex flex-wrap gap-2">
                        {mockTableData.map((item) => (
                            <span key={item.id} className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                                {item.licensePlate}
                            </span>
                        ))}
                    </div>
                </div>
            </div>
        )
    ],
    parameters: {
        docs: {
            description: {
                story: `
          Interactive demo showing the search functionality with available options displayed.
          Perfect for understanding the user experience and available data.
        `
            }
        }
    }
};

export const ManyOptions: Story = {
    args: {
        ...defaultProps,
        tableData: [
            ...mockTableData,
            ...Array.from({ length: 20 }, (_, i) => ({
                ...mockTableData[0],
                id: `WGH${String(i + 5).padStart(3, '0')}`,
                licensePlate: `PLT${String(i + 1).padStart(3, '0')}`,
            })),
        ],
    },
    parameters: {
        docs: {
            description: {
                story: `
          Example with many license plate options to demonstrate the search and scroll functionality.
          Shows how the component handles larger datasets efficiently.
        `
            }
        }
    }
};

export const Responsive: Story = {
    args: defaultProps,
    decorators: [
        (Story) => (
            <div className="space-y-4">
                <div className="p-4 border rounded">
                    <h4 className="font-medium mb-2">Desktop View</h4>
                    <div className="max-w-sm">
                        <Story />
                    </div>
                </div>
                <div className="p-4 border rounded">
                    <h4 className="font-medium mb-2">Mobile View</h4>
                    <div className="max-w-xs">
                        <Story />
                    </div>
                </div>
            </div>
        )
    ],
    parameters: {
        docs: {
            description: {
                story: `
          Demonstrates the responsive behavior of the license plate filter across different screen sizes.
          Shows how the component adapts its layout and interaction patterns.
        `
            }
        }
    }
};
