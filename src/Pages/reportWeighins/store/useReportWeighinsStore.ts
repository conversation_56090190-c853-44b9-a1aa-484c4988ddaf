import { create } from 'zustand';
import { ReportWeighins } from '@/Pages/reportWeighins/models';

type ReportWeighinsStoreT = {
  url: string;
  downloadReportUrl: string;
  tableData: ReportWeighins[];
  setUrlStore: (url: string) => void;
  setDownloadReportUrl: (url: string) => void;
  setTableData: (data: ReportWeighins[]) => void;
};

const useUrlReportWeighinsStore = create<ReportWeighinsStoreT>((set) => ({
  url: '',
  downloadReportUrl: '',
  tableData: [],
  setUrlStore: (url: string) => set({ url: url }),
  setDownloadReportUrl: (url: string) => set({ downloadReportUrl: url }),
  setTableData: (data: ReportWeighins[]) => set({ tableData: data }),
}));

export { useUrlReportWeighinsStore };
