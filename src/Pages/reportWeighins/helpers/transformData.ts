import { ReportWeighins } from '../models';
import dayjs from 'dayjs';

export const transformData = (data: ReportWeighins[]) => {
  return data.map((item) => ({
    ...item,
    arrivingWeight: `${item.arrivingWeight.toLocaleString()} kg`,
    leavingWeight: `${item.leavingWeight.toLocaleString()} kg`,
    depositWeight: `${item.depositWeight.toLocaleString()} kg`,
    entryDate: dayjs(item.entryDate).format('DD/MM/YYYY HH:mm'),
    egressDate: dayjs(item.egressDate).format('DD/MM/YYYY HH:mm'),
    loadingDate: dayjs(item.loadingDate).format('DD/MM/YYYY HH:mm'),
    cancelDate: item.cancelDate && dayjs(item.cancelDate).format('DD/MM/YYYY HH:mm') || 'N/A',
    town: item.town.name,
    city: item.town.name,
    department: item.town.department,
    province: item.town.province,
  }));
}