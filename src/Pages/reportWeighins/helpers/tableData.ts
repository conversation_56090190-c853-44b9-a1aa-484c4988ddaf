import { IColumnsFilters } from '@/Orion.CORE.Frontend/models/StoreModels/masterTableStoreV2.type';

export const stateColFilters: IColumnsFilters = {
  columns: [
    {
      key: 'id',
      label: 'Nro Comprobante',
      width: 120,
    },
    {
      key: 'licensePlate',
      label: 'Placa',
      width: 90,
    },
    {
      key: 'entryDate',
      label: 'Fecha de ingreso / Hora de ingreso',
      width: 180,
    },
    {
      key: 'egressDate',
      label: 'Fecha de egreso / Hora de egreso',
      width: 180,
    },
    {
      key: 'arrivingWeight',
      label: 'Peso de llegada',
      width: 110,
    },
    {
      key: 'leavingWeight',
      label: 'Peso de salida',
      width: 110,
    },
    {
      key: 'depositWeight',
      label: 'Peso depósito',
      width: 110,
    },
    {
      key: 'nuap',
      label: 'NUAP',
      width: 90,
    },
    {
      key: 'originType',
      label: 'Tipo de Origen',
      width: 120,
    },
    {
      key: 'nit',
      label: 'NIT',
      width: 120,
    },
    {
      key: 'companyName',
      label: 'Nombre de la Empresa',
      width: 200,
    },
    {
      key: 'materialType',
      label: 'Código Tipo Material',
      width: 150,
    },
    {
      key: 'depositPlace',
      label: 'Lugar de depósito',
      width: 130,
    },
    {
      key: 'loadingDate',
      label: 'Fecha de Carga',
      width: 140,
    },
    {
      key: 'cancelDate',
      label: 'Fecha de Cancelación',
      width: 150,
    },
    {
      key: 'loadingType',
      label: 'Tipo de Carga',
      width: 120,
    },
    {
      key: 'town',
      label: 'Municipio Viaje',
      width: 130,
    },
    {
      key: 'city',
      label: 'Ciudad',
      width: 120,
    },
    {
      key: 'department',
      label: 'Departamento',
      width: 130,
    },
    {
      key: 'province',
      label: 'Provincia',
      width: 160,
    }
  ],
  filters: [],
};
