import { useReport } from './hook/useReport';
import { Grid } from '@mui/material';
import TableMasterV2 from '../../Orion.CORE.Frontend/components/TableV2/TableMaster.component';
import { NoResults, Paginator } from '@/Orion.CORE.Frontend/components';
import { stateColFilters } from './helpers/tableData';
import { Paths } from '@/router/utilities/paths.ts';
import { DataFilterT } from '@/Orion.CORE.Frontend/models';
import React, { useEffect, useState } from 'react';
import { useFiltersStore } from '@/Orion.CORE.Frontend/store';
import { getLocalStorage } from '@/Orion.CORE.Frontend/helpers';
import { FilterWeighinsE, nameModule, transformData } from './helpers';
import { nameModules } from '@/constants/nameModule';
import TableSkeleton from '@/components/TableLoadingSkeleton.tsx';
import Header from '@/components/Header.tsx';
import ReportFiltersDrawer from '../../components/ReportFiltersDrawer.tsx';
import ErrorInformation from '@/components/ErrorInformationMessage.tsx';
import FiltersWeighins from '@/Pages/reportWeighins/components/FiltersWeighins.tsx';
import { useUrlReportWeighinsStore } from '@/Pages/reportWeighins/store/useReportWeighinsStore.ts';

const filterOpenInit: Record<string, DataFilterT> = {
  [FilterWeighinsE.startDate]: {
    open: false,
    label: 'Fecha de inicio',
  },
  [FilterWeighinsE.endDate]: {
    open: false,
    label: 'Fecha de fin',
  },
  [FilterWeighinsE.licensePlate]: {
    open: false,
    label: 'Placa de vehículo',
  },
  [FilterWeighinsE.ticketNumber]: {
    open: false,
    label: 'Número de ticket',
  },
  [FilterWeighinsE.company]: {
    open: false,
    label: 'Empresa',
  },
};

const ReportWeighins = () => {
  const [pageNumber, setpageNumber] = useState(1);
  const setInitStateDrawer = useFiltersStore((state) => state.setInitStateDrawer);
  const setStatePoppers = useFiltersStore((state) => state.setStatePoppers);
  const setIsFiltersApplied = useFiltersStore((state) => state.setIsFiltersApplied);
  const urlResult = useUrlReportWeighinsStore((state) => state.url);
  const setTableData = useUrlReportWeighinsStore((state) => state.setTableData);

  const isApplied = getLocalStorage(nameModule) ?? true;

  useEffect(() => {
    setInitStateDrawer(nameModules.FILTERWEIGHINS, 'right');
    setStatePoppers(filterOpenInit);
    setIsFiltersApplied(nameModules.FILTERWEIGHINS, isApplied);
    setpageNumber(1);
  }, [urlResult]);

  const { table } = useReport(pageNumber);
  const { responseWeighinsReport, dataTable, data } = table;
  const { error } = responseWeighinsReport;

  useEffect(() => {
    if (dataTable.results) {
      setTableData(dataTable.results);
    }
  }, [dataTable.results, setTableData]);

  const paginationSize = dataTable.totalRecords;

  const ReportHeader = () => {
    return (
      <div className="flex justify-between w-full items-center py-5">
        <Header title="Pesajes">
          <ReportFiltersDrawer
            isDownloadable={true}
            moduleName={nameModules.FILTERWEIGHINS}
            moduleFiltersName={nameModule}
          >
            <FiltersWeighins title="Filtros" />
          </ReportFiltersDrawer>
        </Header>
      </div>
    );
  };

  return (
    <>
      <ReportHeader />
      <div className="container min-w-full px-5">
        {responseWeighinsReport.isError ? (
          <Grid container width="100%" alignContent="flex-start" justifyContent="center" height={'80%'} maxHeight={'80%'}>
            <ErrorInformation messageError={error?.response?.data.Messages} />
          </Grid>
        ) : responseWeighinsReport?.isFetching ? (
          <TableSkeleton />
        ) : (
          <>
            <Grid container alignContent="flex-start" justifyContent="center" height={'auto'} maxHeight={'80%'}>
              {data?.data && data?.data?.results.length > 0 ? (
                <>
                  <TableMasterV2 stateColFilters={stateColFilters} data={transformData(dataTable.results)} nameModule={Paths.WEIGHINS} tableActions={[]} />
                  <Grid width="100%" pt={1}>
                    <Paginator
                      start={(pageNumber - 1) * 25 + 1}
                      end={(pageNumber - 1) * 25 + 25 > paginationSize ? paginationSize : (pageNumber - 1) * 25 + 25}
                      total={paginationSize}
                      pageCurrent={pageNumber}
                      countPage={Math.round(paginationSize / 25) === 0 ? 1 : Math.round(paginationSize / 25)}
                      handleNextPage={() => setpageNumber(pageNumber + 1)}
                      handlePrevPage={() => setpageNumber(pageNumber - 1)}
                      isFetching={false}
                    />
                  </Grid>
                </>
              ) : (
                !responseWeighinsReport?.isFetching && <NoResults />
              )}
            </Grid>
          </>
        )}
      </div>
    </>
  );
};

export default ReportWeighins;
