import React, { useEffect, useState } from "react";
import { <PERSON>ton, <PERSON>u, MenuItem, Typography } from "@mui/material";
import DownloadIcon from "@mui/icons-material/Download";

import { downloadTable } from "@/helpers/downloadTable.ts";
import { VITE_BASE_URL_MERCURY } from "@/constants";
import { useUrlReport14Store } from "@/Pages/reportFormat14/store/useReport14Store.ts";
import { useUrlReport34Store } from "@/Pages/reportFormat34/store/useReport34Store.ts";
import { useUrlReportWeighinsStore } from "@/Pages/reportWeighins/store/useReportWeighinsStore.ts";
import { useNotification } from "@/hooks/useNotification";

type PropsT = {
  moduleName: string;
};

export default function ReportDownloadOptions({ moduleName }: Readonly<PropsT>) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const report14DownloadUrl = useUrlReport14Store((state) => state.downloadReportUrl);
  const report34DownloadUrl = useUrlReport34Store((state) => state.downloadReportUrl);
  const reportWeighinsDownloadUrl = useUrlReportWeighinsStore((state) => state.downloadReportUrl);
  const { setNotification } = useNotification();

  const [downloadIndex, setDownloadIndex] = useState(0);
  const [format, setFormat] = useState("");
  const [isDownloading, setIsDownloading] = useState(false);

  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = (format: string) => {
    setIsDownloading(true);

    setAnchorEl(null);
    setDownloadIndex((state) => state + 1);
    setFormat(format);

    setTimeout(() => {
      setIsDownloading(false);
    }, 3000);
  };

  useEffect(() => {
    if (downloadIndex > 0) {
      let downloadUrl = "";

      const dictionary: Record<string, string> = {
        filtersFormat14: report14DownloadUrl,
        filtersFormat34: report34DownloadUrl,
        filtersWeighins: reportWeighinsDownloadUrl,
      };
      downloadUrl = dictionary[moduleName];

      if (!downloadUrl) {
        setNotification("error", ["Módulo inválido"], 1500, "top");
        return;
      }
      downloadTable(`${VITE_BASE_URL_MERCURY}${downloadUrl}`, format);
    }
  }, [downloadIndex, moduleName, reportWeighinsDownloadUrl, report14DownloadUrl, report34DownloadUrl, format, setNotification]);

  return (
    <div>
      <Button
        aria-controls={open ? "basic-menu" : undefined}
        aria-expanded={open ? "true" : undefined}
        aria-haspopup="true"
        id="basic-button"
        startIcon={<DownloadIcon className={!isDownloading ? "" : "animate-ping text-[#28a745]"} />}
        onClick={handleClick}
      >
        <Typography className="text-lg font-bold" color="primary">
          Exportar
        </Typography>
      </Button>
      <Menu
        MenuListProps={{
          "aria-labelledby": "basic-button",
        }}
        anchorEl={anchorEl}
        id="basic-menu"
        open={open}
        onClose={handleClose}
      >
        <MenuItem onClick={() => handleClose("xlsx")}>Excel (xlsx)</MenuItem>
        <MenuItem onClick={() => handleClose("xls")}>Excel (xls)</MenuItem>
        <MenuItem onClick={() => handleClose("csv")}>CSV</MenuItem>
      </Menu>
    </div>
  );
}
