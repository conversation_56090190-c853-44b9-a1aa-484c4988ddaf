import { endpointsVersions } from "./index.ts";
import { EMVARIAS_COMPANY_ID } from "./companyIds.ts";

const isLocalhost = () => {
  return window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
};

const buildPath = (path: string) => {
  if (isLocalhost()) {
    const cleanPath = path.replace(/^api\//, '');
    return cleanPath.startsWith('/') ? cleanPath : `/${cleanPath}`;
  }

  return path.startsWith('/') ? path : `/${path}`;
};

export const endpointsPathsBussiness = {
  USER_PARAMETERS: `api/authorization`,
  MASS_BALANCE: buildPath(`api/${endpointsVersions.MERCURY_REPORTS}/Reports/MassBalance`),
  MASS_BALANCE_EXPORT: buildPath(`api/${endpointsVersions.MERCURY_REPORTS}/Reports/MassBalance/Export`),
  GENERATE_DISCOUNT_REPORT: buildPath(`api/${endpointsVersions.MERCURY_REPORTS}/Reports/DiscountsReport`),
  REPORTS_PREVIEW: buildPath(`api/${endpointsVersions.MERCURY_REPORTS}/Reports/Preview`),
  RECYCLING_AREA: buildPath(`api/${endpointsVersions.MERCURY_REPORTS}/RecyclingArea`),
  REPORTS_EXPORT: buildPath(`api/${endpointsVersions.MERCURY_REPORTS}/Reports/Export`),
  WEIGHINS: buildPath(`api/${endpointsVersions.MERCURY_REPORTS}/Weighins`),
  VEHICLES: `api/core/vehicles?companyId=${EMVARIAS_COMPANY_ID}`,
  SERVICE_TYPE: buildPath(`api/v2/companies/${EMVARIAS_COMPANY_ID}/hygieneServiceTypes`),
  NON_COMPLIANCE_MOTIVES: buildPath(`api/v2/companies/${EMVARIAS_COMPANY_ID}/hygieneConfig/nonComplianceMotives`),
  ROUTE: buildPath(`api/v2/companies/${EMVARIAS_COMPANY_ID}/hygieneRoutes`),
  REFRESH_TOKEN: buildPath(`api/authentication/refresh`),
  NIT: buildPath(`api/${endpointsVersions.MERCURY_REPORTS}/Clients`),
  GENERATE_SUI: buildPath(`api/${endpointsVersions.MERCURY_REPORTS}/Reports/ExportCompressedReports`),
  REGULATORY_ROUTES: buildPath(`api/${endpointsVersions.MERCURY_REPORTS}/Microroute/regulatory-routes`),
};
