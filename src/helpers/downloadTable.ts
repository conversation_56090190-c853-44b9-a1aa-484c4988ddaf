import { AuthKeys } from "@/constants/authKeys";

const downloadTable = (url: string, format: string, tableHeaders?: string) => {
  fetch(url, {
    method: "get",
    headers: {
      Authorization: `Bearer ${localStorage.getItem(AuthKeys.accessTokenMercury)}`,
      responseType: "blob",
      "X-ExcelFormat": format,
      "X-ExcelHeaders": tableHeaders ?? "",
    },
  }).then((response) =>
    response.blob().then((blob) => {
      const headerStringFileName = response.headers.get("content-disposition");
      
      if (!headerStringFileName) {
        console.error("No content-disposition header found");
        return;
      }
      
      const regex = /filename[^;\n=]*=([^;\n]*)/;
      const match = regex.exec(headerStringFileName);
      
      if (!match?.[1]) {
        console.error("Could not extract filename from header");
        return;
      }
      
      let stringNameFileAndFormat = match[1];
      stringNameFileAndFormat = stringNameFileAndFormat.replace(/(^")|("$)/g, "");
      const urlBlob = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = urlBlob;
      link.setAttribute("download", stringNameFileAndFormat);
      document.body.appendChild(link);
      link.click();
    }),
  );
};

export { downloadTable };
